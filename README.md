# 🪟 Microwin Linux

A modern Linux port of the Microwin utility for creating custom Windows ISOs. This application allows you to debloat Windows installations, bypass hardware requirements, and create unattended installation media directly from your Arch Linux system.

## ✨ Features

- **🔧 Windows ISO Customization**: Mount and modify Windows 10/11 ISOs
- **🗑️ Debloating**: Remove bloatware, Microsoft Store apps, and unnecessary Windows features
- **⚙️ Registry Modifications**: Bypass Windows 11 hardware requirements, disable telemetry, and configure OOBE settings
- **👤 User Account Setup**: Create user accounts with optional passwords and automatic login
- **💾 Driver Injection**: Add custom drivers and VirtIO drivers for virtual machines
- **📦 Unattended Installation**: Generate unattend.xml files for automated Windows setup
- **🎨 Modern UI**: Clean, responsive interface built with React and Tauri

## 🛠️ Technology Stack

- **Backend**: Rust with Tauri framework
- **Frontend**: React with TypeScript
- **ISO Manipulation**: wimlib-imagex (Linux equivalent of Windows DISM)
- **Registry Editing**: hivex for offline Windows registry modification
- **ISO Creation**: genisoimage/mkisofs for bootable ISO generation

## 📋 Prerequisites

### System Requirements
- Arch Linux (or compatible distribution)
- Rust toolchain (1.70+)
- Node.js and pnpm
- sudo privileges for ISO mounting

### Required Packages
```bash
sudo pacman -S wimlib cdrtools rust nodejs pnpm
```

### Development Dependencies
```bash
cargo install tauri-cli
```

## 🚀 Installation & Usage

### 1. Clone and Setup
```bash
git clone <repository-url>
cd microwin-linux
pnpm install
```

### 2. Development Mode
```bash
cargo tauri dev
```

### 3. Build for Production
```bash
cargo tauri build
```

## 📖 How to Use

### Step 1: Select Windows ISO
1. Click "📁 Select Windows ISO" to choose your Windows 10 or 11 ISO file
2. The application will automatically detect available Windows editions
3. Select the edition you want to customize

### Step 2: Configure Settings

#### User Account
- Set username and optional password
- Configure automatic login preferences

#### System Modifications
- ✅ **Bypass Windows 11 hardware requirements**: Removes TPM, Secure Boot, and RAM restrictions
- ✅ **Disable telemetry and tracking**: Blocks Microsoft data collection
- ✅ **Set dark theme by default**: Configures Windows to use dark mode

#### Debloating Options
- ✅ **Remove Microsoft Store apps**: Removes pre-installed bloatware
- ✅ **Remove Windows capabilities**: Removes Internet Explorer, WordPad, etc.

### Step 3: Create Custom ISO
1. Select output location for your custom ISO
2. Click "🚀 Start Processing" to begin
3. Monitor progress and logs in real-time
4. Your custom Windows ISO will be created with all modifications applied

## 🔧 Technical Details

### Core Modules

1. **ISO Handler**: Mounts ISOs, extracts WIM files, manages temporary directories
2. **Registry Editor**: Modifies Windows registry hives using hivex
3. **Debloater**: Removes packages and features using wimlib-imagex
4. **Unattend Generator**: Creates unattend.xml for automated installation
5. **Driver Injector**: Adds custom and VirtIO drivers
6. **ISO Builder**: Creates bootable ISOs with proper UEFI/BIOS support

### Linux Tool Equivalents

| Windows Tool | Linux Equivalent | Purpose |
|--------------|------------------|---------|
| DISM | wimlib-imagex | WIM file manipulation |
| Registry Editor | hivex | Offline registry editing |
| Windows ADK | genisoimage | ISO creation |

## 🐛 Troubleshooting

### Common Issues

**ISO mounting fails**:
- Ensure you have sudo privileges
- Check that the ISO file is not corrupted
- Verify wimlib is installed: `wimlib-imagex --version`

**Registry modifications not applied**:
- Ensure hivex is installed: `hivexregedit --version`
- Check that the Windows image is not corrupted

**ISO creation fails**:
- Verify genisoimage is installed: `genisoimage --version`
- Ensure sufficient disk space for output ISO

### Logs and Debugging
- Application logs are displayed in real-time during processing
- Check the terminal output for detailed error messages
- Enable debug logging by setting `RUST_LOG=debug`

## 🤝 Contributing

Contributions are welcome! Please feel free to submit issues, feature requests, or pull requests.

### Development Setup
1. Install all prerequisites
2. Clone the repository
3. Run `pnpm install` to install frontend dependencies
4. Run `cargo tauri dev` to start development server

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **ChrisTitusTech**: Original Microwin PowerShell implementation
- **Tauri Team**: Modern desktop application framework
- **wimlib**: Cross-platform library for Windows imaging
- **hivex**: Library for reading and writing Windows Registry hive files

## ⚠️ Disclaimer

This tool is for educational and legitimate system administration purposes only. Always ensure you have proper licenses for any Windows installations you modify. The authors are not responsible for any misuse of this software.
