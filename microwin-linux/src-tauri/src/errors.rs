use serde::{Deserialize, Serialize};
use std::fmt;

/// Custom error types for Microwin operations
#[derive(Debug, Serialize, Deserialize)]
pub enum MicrowinError {
    /// ISO file operations errors
    IsoError(String),
    /// Registry editing errors
    RegistryError(String),
    /// WIM file manipulation errors
    WimError(String),
    /// Driver injection errors
    DriverError(String),
    /// File system operations errors
    FileSystemError(String),
    /// Process execution errors
    ProcessError(String),
    /// Configuration errors
    ConfigError(String),
    /// Validation errors
    ValidationError(String),
    /// Generic errors
    GenericError(String),
}

impl fmt::Display for MicrowinError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            MicrowinError::IsoError(msg) => write!(f, "ISO Error: {}", msg),
            MicrowinError::RegistryError(msg) => write!(f, "Registry Error: {}", msg),
            MicrowinError::WimError(msg) => write!(f, "WIM Error: {}", msg),
            MicrowinError::DriverError(msg) => write!(f, "Driver Error: {}", msg),
            MicrowinError::FileSystemError(msg) => write!(f, "File System Error: {}", msg),
            MicrowinError::ProcessError(msg) => write!(f, "Process Error: {}", msg),
            MicrowinError::ConfigError(msg) => write!(f, "Configuration Error: {}", msg),
            MicrowinError::ValidationError(msg) => write!(f, "Validation Error: {}", msg),
            MicrowinError::GenericError(msg) => write!(f, "Error: {}", msg),
        }
    }
}

impl std::error::Error for MicrowinError {}

impl From<std::io::Error> for MicrowinError {
    fn from(error: std::io::Error) -> Self {
        MicrowinError::FileSystemError(error.to_string())
    }
}

impl From<serde_json::Error> for MicrowinError {
    fn from(error: serde_json::Error) -> Self {
        MicrowinError::ConfigError(error.to_string())
    }
}

impl From<anyhow::Error> for MicrowinError {
    fn from(error: anyhow::Error) -> Self {
        MicrowinError::GenericError(error.to_string())
    }
}

/// Result type alias for Microwin operations
pub type MicrowinResult<T> = Result<T, MicrowinError>;

/// Helper macro for creating errors
#[macro_export]
macro_rules! microwin_error {
    ($variant:ident, $msg:expr) => {
        MicrowinError::$variant($msg.to_string())
    };
    ($variant:ident, $fmt:expr, $($arg:tt)*) => {
        MicrowinError::$variant(format!($fmt, $($arg)*))
    };
}

/// Helper function to convert command output to error
pub fn command_error(command: &str, output: &std::process::Output) -> MicrowinError {
    let stderr = String::from_utf8_lossy(&output.stderr);
    let stdout = String::from_utf8_lossy(&output.stdout);
    
    MicrowinError::ProcessError(format!(
        "Command '{}' failed with exit code {}\nStdout: {}\nStderr: {}",
        command,
        output.status.code().unwrap_or(-1),
        stdout,
        stderr
    ))
}
