use crate::config::{WindowsImageInfo, TempSettings};
use crate::errors::{MicrowinError, MicrowinResult, command_error};
use crate::microwin_error;
use std::path::{Path, PathBuf};
use std::process::Command;
use std::fs;
use tempfile::TempDir;
use uuid::Uuid;
use log::{info, warn, error, debug};

/// Handles ISO mounting, unmounting, and basic file operations
pub struct IsoHandler {
    /// Current mount point
    mount_point: Option<PathBuf>,
    /// Temporary directory for operations
    temp_dir: Option<TempDir>,
    /// Scratch directory for WIM extraction
    scratch_dir: Option<PathBuf>,
    /// Original ISO path
    iso_path: Option<PathBuf>,
}

impl IsoHandler {
    /// Create a new ISO handler
    pub fn new() -> Self {
        Self {
            mount_point: None,
            temp_dir: None,
            scratch_dir: None,
            iso_path: None,
        }
    }

    /// Mount an ISO file and return the mount point
    pub fn mount_iso(&mut self, iso_path: &Path) -> MicrowinResult<PathBuf> {
        info!("Mounting ISO: {}", iso_path.display());

        // Validate ISO file exists
        if !iso_path.exists() {
            return Err(microwin_error!(IsoError, "ISO file does not exist: {}", iso_path.display()));
        }

        // Create temporary mount point
        let temp_dir = TempDir::new()
            .map_err(|e| microwin_error!(IsoError, "Failed to create temp directory: {}", e))?;
        
        let mount_point = temp_dir.path().join("mount");
        fs::create_dir_all(&mount_point)?;

        // Mount the ISO using system mount command
        let output = Command::new("sudo")
            .args(&[
                "mount", 
                "-o", "loop,ro",
                iso_path.to_str().unwrap(),
                mount_point.to_str().unwrap()
            ])
            .output()
            .map_err(|e| microwin_error!(IsoError, "Failed to execute mount command: {}", e))?;

        if !output.status.success() {
            return Err(command_error("mount", &output));
        }

        info!("ISO mounted successfully at: {}", mount_point.display());
        
        self.mount_point = Some(mount_point.clone());
        self.temp_dir = Some(temp_dir);
        self.iso_path = Some(iso_path.to_path_buf());

        Ok(mount_point)
    }

    /// Unmount the currently mounted ISO
    pub fn unmount_iso(&mut self) -> MicrowinResult<()> {
        if let Some(mount_point) = &self.mount_point {
            info!("Unmounting ISO from: {}", mount_point.display());

            let output = Command::new("sudo")
                .args(&["umount", mount_point.to_str().unwrap()])
                .output()
                .map_err(|e| microwin_error!(IsoError, "Failed to execute umount command: {}", e))?;

            if !output.status.success() {
                warn!("Failed to unmount ISO: {}", String::from_utf8_lossy(&output.stderr));
                // Try force unmount
                let force_output = Command::new("sudo")
                    .args(&["umount", "-f", mount_point.to_str().unwrap()])
                    .output()
                    .map_err(|e| microwin_error!(IsoError, "Failed to force unmount: {}", e))?;

                if !force_output.status.success() {
                    return Err(command_error("umount -f", &force_output));
                }
            }

            self.mount_point = None;
            info!("ISO unmounted successfully");
        }

        Ok(())
    }

    /// Extract WIM file information from mounted ISO
    pub fn get_wim_info(&self) -> MicrowinResult<Vec<WindowsImageInfo>> {
        let mount_point = self.mount_point.as_ref()
            .ok_or_else(|| microwin_error!(IsoError, "No ISO currently mounted"))?;

        let wim_path = mount_point.join("sources").join("install.wim");
        let esd_path = mount_point.join("sources").join("install.esd");

        let image_path = if wim_path.exists() {
            wim_path
        } else if esd_path.exists() {
            esd_path
        } else {
            return Err(microwin_error!(IsoError, "Neither install.wim nor install.esd found in ISO"));
        };

        info!("Reading Windows image info from: {}", image_path.display());

        // Use wimlib-imagex to get image information
        let output = Command::new("wimlib-imagex")
            .args(&["info", image_path.to_str().unwrap()])
            .output()
            .map_err(|e| microwin_error!(WimError, "Failed to execute wimlib-imagex: {}", e))?;

        if !output.status.success() {
            return Err(command_error("wimlib-imagex info", &output));
        }

        let info_text = String::from_utf8_lossy(&output.stdout);
        self.parse_wim_info(&info_text)
    }

    /// Parse wimlib-imagex info output
    fn parse_wim_info(&self, info_text: &str) -> MicrowinResult<Vec<WindowsImageInfo>> {
        let mut images = Vec::new();
        let mut current_image: Option<WindowsImageInfo> = None;

        for line in info_text.lines() {
            let line = line.trim();
            
            if line.starts_with("Index:") {
                // Save previous image if exists
                if let Some(image) = current_image.take() {
                    images.push(image);
                }
                
                // Start new image
                let index = line.split(':').nth(1)
                    .and_then(|s| s.trim().parse::<u32>().ok())
                    .unwrap_or(0);
                
                current_image = Some(WindowsImageInfo {
                    index,
                    name: String::new(),
                    description: String::new(),
                    version: String::new(),
                    architecture: String::new(),
                    size: 0,
                });
            } else if let Some(ref mut image) = current_image {
                if line.starts_with("Name:") {
                    image.name = line.split(':').nth(1).unwrap_or("").trim().to_string();
                } else if line.starts_with("Description:") {
                    image.description = line.split(':').nth(1).unwrap_or("").trim().to_string();
                } else if line.starts_with("Version:") {
                    image.version = line.split(':').nth(1).unwrap_or("").trim().to_string();
                } else if line.starts_with("Architecture:") {
                    image.architecture = line.split(':').nth(1).unwrap_or("").trim().to_string();
                } else if line.starts_with("Total Bytes:") {
                    image.size = line.split(':').nth(1)
                        .and_then(|s| s.trim().parse::<u64>().ok())
                        .unwrap_or(0);
                }
            }
        }

        // Add the last image
        if let Some(image) = current_image {
            images.push(image);
        }

        if images.is_empty() {
            return Err(microwin_error!(WimError, "No Windows images found in WIM file"));
        }

        info!("Found {} Windows images", images.len());
        Ok(images)
    }

    /// Copy ISO contents to a working directory
    pub fn copy_iso_contents(&self, dest_dir: &Path) -> MicrowinResult<()> {
        let mount_point = self.mount_point.as_ref()
            .ok_or_else(|| microwin_error!(IsoError, "No ISO currently mounted"))?;

        info!("Copying ISO contents from {} to {}", mount_point.display(), dest_dir.display());

        // Create destination directory
        fs::create_dir_all(dest_dir)?;

        // Use cp command for efficient copying
        let output = Command::new("cp")
            .args(&["-r", mount_point.to_str().unwrap(), dest_dir.to_str().unwrap()])
            .output()
            .map_err(|e| microwin_error!(FileSystemError, "Failed to copy ISO contents: {}", e))?;

        if !output.status.success() {
            return Err(command_error("cp", &output));
        }

        info!("ISO contents copied successfully");
        Ok(())
    }

    /// Get the current mount point
    pub fn get_mount_point(&self) -> Option<&PathBuf> {
        self.mount_point.as_ref()
    }

    /// Create scratch directory for WIM operations
    pub fn create_scratch_dir(&mut self) -> MicrowinResult<PathBuf> {
        let scratch_dir = std::env::temp_dir().join(format!("microwin_scratch_{}", Uuid::new_v4()));
        fs::create_dir_all(&scratch_dir)?;
        
        info!("Created scratch directory: {}", scratch_dir.display());
        self.scratch_dir = Some(scratch_dir.clone());
        Ok(scratch_dir)
    }

    /// Get the scratch directory
    pub fn get_scratch_dir(&self) -> Option<&PathBuf> {
        self.scratch_dir.as_ref()
    }
}

impl Drop for IsoHandler {
    fn drop(&mut self) {
        // Cleanup on drop
        if let Err(e) = self.unmount_iso() {
            error!("Failed to unmount ISO during cleanup: {}", e);
        }
        
        if let Some(scratch_dir) = &self.scratch_dir {
            if let Err(e) = fs::remove_dir_all(scratch_dir) {
                error!("Failed to remove scratch directory during cleanup: {}", e);
            }
        }
    }
}
