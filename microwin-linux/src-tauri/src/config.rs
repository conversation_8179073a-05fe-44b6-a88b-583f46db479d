use serde::{Deserialize, Serialize};
use std::path::PathBuf;

/// Main configuration for Microwin processing
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct MicrowinConfig {
    /// Input ISO file path
    pub iso_path: String,
    /// Output ISO file path
    pub output_path: String,
    /// User account settings
    pub user_settings: UserSettings,
    /// Driver injection settings
    pub driver_settings: DriverSettings,
    /// Debloating configuration
    pub debloat_settings: DebloatSettings,
    /// Registry modification settings
    pub registry_settings: RegistrySettings,
    /// Temporary directories
    pub temp_settings: TempSettings,
}

/// User account configuration for Windows installation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserSettings {
    /// Username for the default account
    pub username: String,
    /// Optional password (empty for no password)
    pub password: Option<String>,
    /// Whether to enable automatic login
    pub auto_login: bool,
    /// Language and region settings
    pub language: String,
    /// Timezone setting
    pub timezone: Option<String>,
}

/// Driver injection configuration
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct DriverSettings {
    /// Whether to inject custom drivers
    pub inject_drivers: bool,
    /// Path to custom driver directory
    pub driver_path: Option<String>,
    /// Whether to include VirtIO drivers
    pub include_virtio: bool,
    /// Whether to export drivers from current system
    pub export_current_drivers: bool,
}

/// Debloating configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DebloatSettings {
    /// Whether to remove Microsoft Store apps
    pub remove_store_apps: bool,
    /// Whether to remove Windows capabilities
    pub remove_capabilities: bool,
    /// Whether to remove optional features
    pub remove_features: bool,
    /// Custom list of packages to remove
    pub custom_packages: Vec<String>,
    /// Debloating profile (minimal, standard, aggressive)
    pub profile: DebloatProfile,
}

/// Registry modification settings
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RegistrySettings {
    /// Whether to bypass Windows 11 hardware requirements
    pub bypass_requirements: bool,
    /// Whether to disable telemetry
    pub disable_telemetry: bool,
    /// Whether to configure OOBE settings
    pub configure_oobe: bool,
    /// Whether to set dark theme
    pub set_dark_theme: bool,
    /// Whether to disable Windows Update
    pub disable_windows_update: bool,
}

/// Temporary directory settings
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TempSettings {
    /// Base temporary directory
    pub temp_dir: Option<String>,
    /// Mount point for ISO
    pub mount_dir: Option<String>,
    /// Scratch directory for WIM extraction
    pub scratch_dir: Option<String>,
    /// Whether to cleanup on completion
    pub cleanup_on_completion: bool,
}

/// Debloating profile levels
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DebloatProfile {
    /// Minimal debloating - only remove obvious bloatware
    Minimal,
    /// Standard debloating - remove most unnecessary apps
    Standard,
    /// Aggressive debloating - remove everything possible
    Aggressive,
    /// Custom debloating - user-defined package list
    Custom,
}

impl Default for MicrowinConfig {
    fn default() -> Self {
        Self {
            iso_path: String::new(),
            output_path: String::new(),
            user_settings: UserSettings::default(),
            driver_settings: DriverSettings::default(),
            debloat_settings: DebloatSettings::default(),
            registry_settings: RegistrySettings::default(),
            temp_settings: TempSettings::default(),
        }
    }
}

impl Default for UserSettings {
    fn default() -> Self {
        Self {
            username: "User".to_string(),
            password: None,
            auto_login: true,
            language: "en-US".to_string(),
            timezone: None,
        }
    }
}

impl Default for DriverSettings {
    fn default() -> Self {
        Self {
            inject_drivers: false,
            driver_path: None,
            include_virtio: false,
            export_current_drivers: false,
        }
    }
}

impl Default for DebloatSettings {
    fn default() -> Self {
        Self {
            remove_store_apps: true,
            remove_capabilities: true,
            remove_features: true,
            custom_packages: Vec::new(),
            profile: DebloatProfile::Standard,
        }
    }
}

impl Default for RegistrySettings {
    fn default() -> Self {
        Self {
            bypass_requirements: true,
            disable_telemetry: true,
            configure_oobe: true,
            set_dark_theme: true,
            disable_windows_update: false,
        }
    }
}

impl Default for TempSettings {
    fn default() -> Self {
        Self {
            temp_dir: None,
            mount_dir: None,
            scratch_dir: None,
            cleanup_on_completion: true,
        }
    }
}

/// Windows image information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WindowsImageInfo {
    /// Image index
    pub index: u32,
    /// Image name/edition
    pub name: String,
    /// Image description
    pub description: String,
    /// Image version
    pub version: String,
    /// Architecture (x64, x86, arm64)
    pub architecture: String,
    /// Image size in bytes
    pub size: u64,
}

/// Processing status for frontend updates
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessingStatus {
    /// Current step being executed
    pub current_step: String,
    /// Progress percentage (0-100)
    pub progress: u8,
    /// Whether processing is complete
    pub completed: bool,
    /// Error message if any
    pub error: Option<String>,
    /// Detailed log messages
    pub logs: Vec<String>,
}
