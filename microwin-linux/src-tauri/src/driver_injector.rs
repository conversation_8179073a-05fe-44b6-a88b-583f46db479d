use crate::config::DriverSettings;
use crate::errors::{MicrowinError, MicrowinResult, command_error};
use crate::microwin_error;
use std::path::{Path, PathBuf};
use std::process::Command;
use std::fs;
use log::{info, warn, error};

/// Handles driver injection into Windows images
pub struct DriverInjector {
    /// Path to the WIM file
    wim_path: PathBuf,
    /// Image index to modify
    image_index: u32,
    /// Temporary directory for driver operations
    temp_dir: PathBuf,
}

impl DriverInjector {
    /// Create a new driver injector
    pub fn new(wim_path: PathBuf, image_index: u32) -> MicrowinResult<Self> {
        let temp_dir = std::env::temp_dir().join(format!("microwin_drivers_{}", uuid::Uuid::new_v4()));
        fs::create_dir_all(&temp_dir)
            .map_err(|e| microwin_error!(DriverError, "Failed to create temp directory: {}", e))?;

        Ok(Self {
            wim_path,
            image_index,
            temp_dir,
        })
    }

    /// Apply driver injection based on settings
    pub fn inject_drivers(&self, settings: &DriverSettings) -> MicrowinResult<()> {
        info!("Starting driver injection process");

        if settings.include_virtio {
            self.download_and_inject_virtio_drivers()?;
        }

        if settings.inject_drivers {
            if let Some(driver_path) = &settings.driver_path {
                self.inject_custom_drivers(Path::new(driver_path))?;
            }
        }

        if settings.export_current_drivers {
            self.export_current_system_drivers()?;
        }

        info!("Driver injection completed successfully");
        Ok(())
    }

    /// Download and inject VirtIO drivers
    fn download_and_inject_virtio_drivers(&self) -> MicrowinResult<()> {
        info!("Downloading and injecting VirtIO drivers");

        let virtio_url = "https://fedorapeople.org/groups/virt/virtio-win/direct-downloads/stable-virtio/virtio-win.iso";
        let virtio_iso_path = self.temp_dir.join("virtio-win.iso");
        let virtio_mount_path = self.temp_dir.join("virtio_mount");

        // Download VirtIO ISO
        self.download_virtio_iso(virtio_url, &virtio_iso_path)?;

        // Mount VirtIO ISO
        fs::create_dir_all(&virtio_mount_path)?;
        self.mount_iso(&virtio_iso_path, &virtio_mount_path)?;

        // Inject VirtIO drivers
        self.inject_virtio_drivers_from_mount(&virtio_mount_path)?;

        // Cleanup
        self.unmount_iso(&virtio_mount_path)?;
        let _ = fs::remove_file(&virtio_iso_path);

        Ok(())
    }

    /// Download VirtIO ISO
    fn download_virtio_iso(&self, url: &str, output_path: &Path) -> MicrowinResult<()> {
        info!("Downloading VirtIO drivers from: {}", url);

        let output = Command::new("curl")
            .args(&[
                "-L",
                "-o",
                output_path.to_str().unwrap(),
                url,
            ])
            .output()
            .map_err(|e| microwin_error!(DriverError, "Failed to execute curl: {}", e))?;

        if !output.status.success() {
            return Err(command_error("curl", &output));
        }

        info!("VirtIO ISO downloaded successfully");
        Ok(())
    }

    /// Mount ISO file
    fn mount_iso(&self, iso_path: &Path, mount_path: &Path) -> MicrowinResult<()> {
        let output = Command::new("sudo")
            .args(&[
                "mount",
                "-o", "loop,ro",
                iso_path.to_str().unwrap(),
                mount_path.to_str().unwrap(),
            ])
            .output()
            .map_err(|e| microwin_error!(DriverError, "Failed to mount ISO: {}", e))?;

        if !output.status.success() {
            return Err(command_error("mount", &output));
        }

        Ok(())
    }

    /// Unmount ISO file
    fn unmount_iso(&self, mount_path: &Path) -> MicrowinResult<()> {
        let output = Command::new("sudo")
            .args(&["umount", mount_path.to_str().unwrap()])
            .output()
            .map_err(|e| microwin_error!(DriverError, "Failed to unmount ISO: {}", e))?;

        if !output.status.success() {
            warn!("Failed to unmount ISO: {}", String::from_utf8_lossy(&output.stderr));
        }

        Ok(())
    }

    /// Inject VirtIO drivers from mounted ISO
    fn inject_virtio_drivers_from_mount(&self, mount_path: &Path) -> MicrowinResult<()> {
        info!("Injecting VirtIO drivers from mounted ISO");

        // Common VirtIO driver paths for different Windows versions
        let driver_paths = vec![
            "NetKVM/w10/amd64",  // Network driver
            "viostor/w10/amd64", // Storage driver
            "vioscsi/w10/amd64", // SCSI driver
            "vioser/w10/amd64",  // Serial driver
            "Balloon/w10/amd64", // Memory balloon driver
            "pvpanic/w10/amd64", // Panic driver
        ];

        for driver_path in driver_paths {
            let full_driver_path = mount_path.join(driver_path);
            if full_driver_path.exists() {
                self.inject_driver_directory(&full_driver_path)?;
            } else {
                warn!("VirtIO driver path not found: {}", full_driver_path.display());
            }
        }

        Ok(())
    }

    /// Inject custom drivers from a directory
    fn inject_custom_drivers(&self, driver_path: &Path) -> MicrowinResult<()> {
        info!("Injecting custom drivers from: {}", driver_path.display());

        if !driver_path.exists() {
            return Err(microwin_error!(DriverError, "Driver path does not exist: {}", driver_path.display()));
        }

        if driver_path.is_file() {
            // Single driver file
            self.inject_single_driver(driver_path)?;
        } else {
            // Directory of drivers
            self.inject_driver_directory(driver_path)?;
        }

        Ok(())
    }

    /// Inject a single driver file
    fn inject_single_driver(&self, driver_file: &Path) -> MicrowinResult<()> {
        info!("Injecting single driver: {}", driver_file.display());

        // Validate driver file
        if !self.is_valid_driver_file(driver_file) {
            return Err(microwin_error!(DriverError, "Invalid driver file: {}", driver_file.display()));
        }

        // Use wimlib to add the driver
        let output = Command::new("wimlib-imagex")
            .args(&[
                "update",
                self.wim_path.to_str().unwrap(),
                &self.image_index.to_string(),
                "--command",
                &format!("add {} /Windows/System32/DriverStore/FileRepository/", driver_file.to_str().unwrap()),
            ])
            .output()
            .map_err(|e| microwin_error!(DriverError, "Failed to inject driver: {}", e))?;

        if !output.status.success() {
            return Err(command_error("wimlib-imagex update", &output));
        }

        Ok(())
    }

    /// Inject all drivers from a directory
    fn inject_driver_directory(&self, driver_dir: &Path) -> MicrowinResult<()> {
        info!("Injecting drivers from directory: {}", driver_dir.display());

        // Find all .inf files in the directory
        let inf_files = self.find_inf_files(driver_dir)?;

        if inf_files.is_empty() {
            warn!("No .inf files found in driver directory: {}", driver_dir.display());
            return Ok(());
        }

        for inf_file in inf_files {
            // Get the directory containing the .inf file
            if let Some(inf_dir) = inf_file.parent() {
                self.inject_driver_package(inf_dir)?;
            }
        }

        Ok(())
    }

    /// Inject a complete driver package
    fn inject_driver_package(&self, package_dir: &Path) -> MicrowinResult<()> {
        info!("Injecting driver package: {}", package_dir.display());

        // Copy the entire driver package to the Windows image
        let target_dir = format!("/Windows/System32/DriverStore/FileRepository/{}/", 
                                package_dir.file_name().unwrap().to_str().unwrap());

        let output = Command::new("wimlib-imagex")
            .args(&[
                "update",
                self.wim_path.to_str().unwrap(),
                &self.image_index.to_string(),
                "--command",
                &format!("add {} {}", package_dir.to_str().unwrap(), target_dir),
            ])
            .output()
            .map_err(|e| microwin_error!(DriverError, "Failed to inject driver package: {}", e))?;

        if !output.status.success() {
            warn!("Failed to inject driver package {}: {}", 
                  package_dir.display(), String::from_utf8_lossy(&output.stderr));
        }

        Ok(())
    }

    /// Find all .inf files in a directory recursively
    fn find_inf_files(&self, dir: &Path) -> MicrowinResult<Vec<PathBuf>> {
        let mut inf_files = Vec::new();

        if dir.is_dir() {
            for entry in fs::read_dir(dir)
                .map_err(|e| microwin_error!(DriverError, "Failed to read directory: {}", e))? {
                let entry = entry
                    .map_err(|e| microwin_error!(DriverError, "Failed to read directory entry: {}", e))?;
                let path = entry.path();

                if path.is_file() && path.extension().and_then(|s| s.to_str()) == Some("inf") {
                    inf_files.push(path);
                } else if path.is_dir() {
                    inf_files.extend(self.find_inf_files(&path)?);
                }
            }
        }

        Ok(inf_files)
    }

    /// Check if a file is a valid driver file
    fn is_valid_driver_file(&self, file_path: &Path) -> bool {
        if let Some(extension) = file_path.extension().and_then(|s| s.to_str()) {
            matches!(extension.to_lowercase().as_str(), "inf" | "sys" | "dll" | "cat")
        } else {
            false
        }
    }

    /// Export current system drivers
    fn export_current_system_drivers(&self) -> MicrowinResult<()> {
        info!("Exporting current system drivers");

        let export_dir = self.temp_dir.join("exported_drivers");
        fs::create_dir_all(&export_dir)?;

        // Use dism equivalent to export drivers (this would need to be run on Windows)
        // For now, we'll create a placeholder implementation
        warn!("Driver export from current system not implemented in Linux environment");
        
        // In a real implementation, you would:
        // 1. Detect current hardware
        // 2. Find corresponding drivers
        // 3. Copy them to the export directory
        // 4. Inject them into the Windows image

        Ok(())
    }

    /// Get list of injected drivers for verification
    pub fn list_injected_drivers(&self) -> MicrowinResult<Vec<String>> {
        info!("Listing injected drivers");

        let output = Command::new("wimlib-imagex")
            .args(&[
                "dir",
                self.wim_path.to_str().unwrap(),
                &self.image_index.to_string(),
                "/Windows/System32/DriverStore/FileRepository",
            ])
            .output()
            .map_err(|e| microwin_error!(DriverError, "Failed to list drivers: {}", e))?;

        if !output.status.success() {
            return Err(command_error("wimlib-imagex dir", &output));
        }

        let output_str = String::from_utf8_lossy(&output.stdout);
        let drivers: Vec<String> = output_str
            .lines()
            .filter_map(|line| {
                if line.contains(".inf") {
                    Some(line.trim().to_string())
                } else {
                    None
                }
            })
            .collect();

        Ok(drivers)
    }
}

impl Drop for DriverInjector {
    fn drop(&mut self) {
        // Cleanup temporary directory
        if let Err(e) = fs::remove_dir_all(&self.temp_dir) {
            error!("Failed to cleanup driver temp directory: {}", e);
        }
    }
}
