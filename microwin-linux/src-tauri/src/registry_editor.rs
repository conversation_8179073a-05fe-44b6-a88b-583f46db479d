use crate::config::RegistrySettings;
use crate::errors::{<PERSON><PERSON><PERSON>rror, MicrowinResult, command_error};
use crate::microwin_error;
use std::path::{Path, PathBuf};
use std::process::Command;
use std::fs;
use log::{info, warn, error};

/// Handles offline Windows registry editing using hivex and chntpw
pub struct RegistryEditor {
    /// Path to the mounted Windows image
    image_path: PathBuf,
    /// Registry hive files
    hive_files: Vec<PathBuf>,
}

impl RegistryEditor {
    /// Create a new registry editor for the given Windows image
    pub fn new(image_path: PathBuf) -> Self {
        Self {
            image_path,
            hive_files: Vec::new(),
        }
    }

    /// Apply all Microwin registry modifications
    pub fn apply_microwin_tweaks(&mut self, settings: &RegistrySettings) -> MicrowinResult<()> {
        info!("Applying Microwin registry tweaks");

        // Find and validate registry hives
        self.find_registry_hives()?;

        if settings.bypass_requirements {
            self.bypass_windows11_requirements()?;
        }

        if settings.disable_telemetry {
            self.disable_telemetry_and_tracking()?;
        }

        if settings.configure_oobe {
            self.configure_oobe_settings()?;
        }

        if settings.set_dark_theme {
            self.set_dark_theme_default()?;
        }

        if settings.disable_windows_update {
            self.disable_windows_update()?;
        }

        info!("Registry tweaks applied successfully");
        Ok(())
    }

    /// Find Windows registry hive files in the image
    fn find_registry_hives(&mut self) -> MicrowinResult<()> {
        let system32_path = self.image_path.join("Windows").join("System32");
        let config_path = system32_path.join("config");

        // Common registry hive files
        let hive_names = ["SYSTEM", "SOFTWARE", "SAM", "SECURITY", "DEFAULT"];
        
        for hive_name in &hive_names {
            let hive_path = config_path.join(hive_name);
            if hive_path.exists() {
                self.hive_files.push(hive_path);
                info!("Found registry hive: {}", hive_name);
            }
        }

        if self.hive_files.is_empty() {
            return Err(microwin_error!(RegistryError, "No registry hives found in Windows image"));
        }

        Ok(())
    }

    /// Bypass Windows 11 hardware requirements
    fn bypass_windows11_requirements(&self) -> MicrowinResult<()> {
        info!("Bypassing Windows 11 hardware requirements");

        // Create registry script for bypassing requirements
        let script_content = r#"
Windows Registry Editor Version 5.00

[HKEY_LOCAL_MACHINE\SYSTEM\Setup\LabConfig]
"BypassTPMCheck"=dword:00000001
"BypassSecureBootCheck"=dword:00000001
"BypassRAMCheck"=dword:00000001
"BypassStorageCheck"=dword:00000001
"BypassCPUCheck"=dword:00000001

[HKEY_LOCAL_MACHINE\SYSTEM\Setup\MoSetup]
"AllowUpgradesWithUnsupportedTPMOrCPU"=dword:00000001
"#;

        self.apply_registry_script(script_content, "bypass_requirements")?;
        Ok(())
    }

    /// Disable telemetry and tracking
    fn disable_telemetry_and_tracking(&self) -> MicrowinResult<()> {
        info!("Disabling telemetry and tracking");

        let script_content = r#"
Windows Registry Editor Version 5.00

[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\DataCollection]
"AllowTelemetry"=dword:00000000
"DoNotShowFeedbackNotifications"=dword:00000001

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\DataCollection]
"AllowTelemetry"=dword:00000000

[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\AdvertisingInfo]
"DisabledByGroupPolicy"=dword:00000001

[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\AppCompat]
"AITEnable"=dword:00000000
"DisableInventory"=dword:00000001

[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\TabletPC]
"PreventHandwritingDataSharing"=dword:00000001

[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\SQMClient\Windows]
"CEIPEnable"=dword:00000000
"#;

        self.apply_registry_script(script_content, "disable_telemetry")?;
        Ok(())
    }

    /// Configure OOBE (Out of Box Experience) settings
    fn configure_oobe_settings(&self) -> MicrowinResult<()> {
        info!("Configuring OOBE settings");

        let script_content = r#"
Windows Registry Editor Version 5.00

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\OOBE]
"PrivacyConsentStatus"=dword:00000000
"SkipMachineOOBE"=dword:00000001
"SkipUserOOBE"=dword:00000001

[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\OOBE]
"DisablePrivacyExperience"=dword:00000001

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System]
"EnableFirstLogonAnimation"=dword:00000000
"#;

        self.apply_registry_script(script_content, "configure_oobe")?;
        Ok(())
    }

    /// Set dark theme as default
    fn set_dark_theme_default(&self) -> MicrowinResult<()> {
        info!("Setting dark theme as default");

        let script_content = r#"
Windows Registry Editor Version 5.00

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Themes\Personalize]
"AppsUseLightTheme"=dword:00000000
"SystemUsesLightTheme"=dword:00000000

[HKEY_USERS\.DEFAULT\SOFTWARE\Microsoft\Windows\CurrentVersion\Themes\Personalize]
"AppsUseLightTheme"=dword:00000000
"SystemUsesLightTheme"=dword:00000000
"#;

        self.apply_registry_script(script_content, "dark_theme")?;
        Ok(())
    }

    /// Disable Windows Update
    fn disable_windows_update(&self) -> MicrowinResult<()> {
        info!("Disabling Windows Update");

        let script_content = r#"
Windows Registry Editor Version 5.00

[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate]
"DoNotConnectToWindowsUpdateInternetLocations"=dword:00000001
"DisableWindowsUpdateAccess"=dword:00000001

[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU]
"NoAutoUpdate"=dword:00000001
"AUOptions"=dword:00000001
"#;

        self.apply_registry_script(script_content, "disable_updates")?;
        Ok(())
    }

    /// Apply a registry script using chntpw
    fn apply_registry_script(&self, script_content: &str, script_name: &str) -> MicrowinResult<()> {
        // Create temporary script file
        let temp_dir = std::env::temp_dir();
        let script_path = temp_dir.join(format!("{}.reg", script_name));
        
        fs::write(&script_path, script_content)
            .map_err(|e| microwin_error!(RegistryError, "Failed to write registry script: {}", e))?;

        // Apply the registry script to each relevant hive
        for hive_path in &self.hive_files {
            if let Some(hive_name) = hive_path.file_name().and_then(|n| n.to_str()) {
                match hive_name.to_uppercase().as_str() {
                    "SYSTEM" | "SOFTWARE" => {
                        self.apply_script_to_hive(&script_path, hive_path)?;
                    }
                    _ => continue,
                }
            }
        }

        // Clean up temporary script file
        let _ = fs::remove_file(&script_path);
        Ok(())
    }

    /// Apply registry script to a specific hive using chntpw
    fn apply_script_to_hive(&self, script_path: &Path, hive_path: &Path) -> MicrowinResult<()> {
        info!("Applying registry script to hive: {}", hive_path.display());

        // Use chntpw to apply registry changes
        let output = Command::new("chntpw")
            .args(&[
                "-e",
                hive_path.to_str().unwrap(),
            ])
            .output()
            .map_err(|e| microwin_error!(RegistryError, "Failed to execute chntpw: {}", e))?;

        if !output.status.success() {
            warn!("chntpw command failed, trying alternative method");
            // Try using hivexregedit as fallback
            self.apply_with_hivex(script_path, hive_path)?;
        }

        Ok(())
    }

    /// Apply registry changes using hivex as fallback
    fn apply_with_hivex(&self, script_path: &Path, hive_path: &Path) -> MicrowinResult<()> {
        info!("Using hivex as fallback for registry modifications");

        // Convert .reg file to hivex format and apply
        let output = Command::new("hivexregedit")
            .args(&[
                "--merge",
                hive_path.to_str().unwrap(),
                script_path.to_str().unwrap(),
            ])
            .output()
            .map_err(|e| microwin_error!(RegistryError, "Failed to execute hivexregedit: {}", e))?;

        if !output.status.success() {
            return Err(command_error("hivexregedit", &output));
        }

        Ok(())
    }
}
