// Microwin Linux modules
pub mod config;
pub mod errors;
pub mod iso_handler;
pub mod registry_editor;
pub mod debloater;
pub mod unattend_generator;
pub mod driver_injector;
pub mod iso_builder;
pub mod workflow;

use config::{MicrowinConfig, WindowsImageInfo, ProcessingStatus};
use errors::{MicrowinError, MicrowinResult};
use iso_handler::IsoHandler;
use workflow::MicrowinWorkflow;
use std::sync::{Arc, Mutex};
use tauri::State;
use log::{info, error};

// Global state for the application
pub struct AppState {
    pub iso_handler: Mutex<IsoHandler>,
    pub config: Mutex<Option<MicrowinConfig>>,
    pub status: Arc<Mutex<ProcessingStatus>>,
}

impl Default for AppState {
    fn default() -> Self {
        Self {
            iso_handler: Mutex::new(IsoHandler::new()),
            config: Mutex::new(None),
            status: Arc::new(Mutex::new(ProcessingStatus {
                current_step: "Ready".to_string(),
                progress: 0,
                completed: false,
                error: None,
                logs: Vec::new(),
            })),
        }
    }
}

/// Select and mount an ISO file
#[tauri::command]
async fn select_iso_file(
    iso_path: String,
    state: State<'_, AppState>,
) -> Result<Vec<WindowsImageInfo>, String> {
    info!("Selecting ISO file: {}", iso_path);

    let mut handler = state.iso_handler.lock().unwrap();

    // Mount the ISO
    let mount_point = handler.mount_iso(std::path::Path::new(&iso_path))
        .map_err(|e| e.to_string())?;

    // Get Windows image information
    let images = handler.get_wim_info()
        .map_err(|e| e.to_string())?;

    info!("Successfully loaded ISO with {} images", images.len());
    Ok(images)
}

/// Get current processing status
#[tauri::command]
async fn get_processing_status(state: State<'_, AppState>) -> Result<ProcessingStatus, String> {
    let status = state.status.lock().unwrap();
    Ok(status.clone())
}

/// Update configuration
#[tauri::command]
async fn update_config(
    config: MicrowinConfig,
    state: State<'_, AppState>,
) -> Result<(), String> {
    info!("Updating configuration");
    let mut app_config = state.config.lock().unwrap();
    *app_config = Some(config);
    Ok(())
}

/// Start the Microwin processing
#[tauri::command]
async fn start_microwin_process(
    state: State<'_, AppState>,
) -> Result<String, String> {
    info!("Starting Microwin process");

    // Get configuration
    let config = {
        let app_config = state.config.lock().unwrap();
        app_config.clone().ok_or("No configuration set")?
    };

    // Validate configuration
    if config.iso_path.is_empty() {
        return Err("No ISO file selected".to_string());
    }

    if config.output_path.is_empty() {
        return Err("No output path specified".to_string());
    }

    // Clone the status Arc for the workflow
    let status_clone = Arc::clone(&state.status);

    // Reset status
    {
        let mut status = state.status.lock().unwrap();
        status.current_step = "Initializing...".to_string();
        status.progress = 0;
        status.completed = false;
        status.error = None;
        status.logs.clear();
    }

    // Execute the workflow in a separate task
    let config_clone = config.clone();
    tokio::spawn(async move {
        match MicrowinWorkflow::new(config_clone, status_clone.clone()) {
            Ok(mut workflow) => {
                match workflow.execute().await {
                    Ok(output_path) => {
                        let mut status = status_clone.lock().unwrap();
                        status.current_step = "Completed successfully!".to_string();
                        status.progress = 100;
                        status.completed = true;
                        status.logs.push(format!("Custom ISO created: {}", output_path));
                    }
                    Err(e) => {
                        let mut status = status_clone.lock().unwrap();
                        status.current_step = "Failed".to_string();
                        status.error = Some(e.to_string());
                        status.completed = true;
                        error!("Microwin workflow failed: {}", e);
                    }
                }
            }
            Err(e) => {
                let mut status = status_clone.lock().unwrap();
                status.current_step = "Failed to initialize".to_string();
                status.error = Some(e.to_string());
                status.completed = true;
                error!("Failed to initialize Microwin workflow: {}", e);
            }
        }
    });

    Ok("Microwin process started successfully".to_string())
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // Initialize logging
    env_logger::init();

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_process::init())
        .plugin(tauri_plugin_shell::init())
        .manage(AppState::default())
        .invoke_handler(tauri::generate_handler![
            select_iso_file,
            get_processing_status,
            update_config,
            start_microwin_process
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
