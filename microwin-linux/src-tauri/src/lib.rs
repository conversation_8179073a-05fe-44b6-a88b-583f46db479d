// Microwin Linux modules
pub mod config;
pub mod errors;
pub mod iso_handler;

use config::{MicrowinConfig, WindowsImageInfo, ProcessingStatus};
use errors::{MicrowinError, MicrowinResult};
use iso_handler::IsoHandler;
use std::sync::Mutex;
use tauri::State;
use log::{info, error};

// Global state for the application
pub struct AppState {
    pub iso_handler: Mutex<IsoHandler>,
    pub config: Mutex<Option<MicrowinConfig>>,
    pub status: Mutex<ProcessingStatus>,
}

impl Default for AppState {
    fn default() -> Self {
        Self {
            iso_handler: Mutex::new(IsoHandler::new()),
            config: Mutex::new(None),
            status: Mutex::new(ProcessingStatus {
                current_step: "Ready".to_string(),
                progress: 0,
                completed: false,
                error: None,
                logs: Vec::new(),
            }),
        }
    }
}

/// Select and mount an ISO file
#[tauri::command]
async fn select_iso_file(
    iso_path: String,
    state: State<'_, AppState>,
) -> Result<Vec<WindowsImageInfo>, String> {
    info!("Selecting ISO file: {}", iso_path);

    let mut handler = state.iso_handler.lock().unwrap();

    // Mount the ISO
    let mount_point = handler.mount_iso(std::path::Path::new(&iso_path))
        .map_err(|e| e.to_string())?;

    // Get Windows image information
    let images = handler.get_wim_info()
        .map_err(|e| e.to_string())?;

    info!("Successfully loaded ISO with {} images", images.len());
    Ok(images)
}

/// Get current processing status
#[tauri::command]
async fn get_processing_status(state: State<'_, AppState>) -> Result<ProcessingStatus, String> {
    let status = state.status.lock().unwrap();
    Ok(status.clone())
}

/// Update configuration
#[tauri::command]
async fn update_config(
    config: MicrowinConfig,
    state: State<'_, AppState>,
) -> Result<(), String> {
    info!("Updating configuration");
    let mut app_config = state.config.lock().unwrap();
    *app_config = Some(config);
    Ok(())
}

/// Start the Microwin processing
#[tauri::command]
async fn start_microwin_process(
    state: State<'_, AppState>,
) -> Result<String, String> {
    info!("Starting Microwin process");

    // Get configuration
    let config = {
        let app_config = state.config.lock().unwrap();
        app_config.clone().ok_or("No configuration set")?
    };

    // Update status
    {
        let mut status = state.status.lock().unwrap();
        status.current_step = "Starting process...".to_string();
        status.progress = 0;
        status.completed = false;
        status.error = None;
    }

    // TODO: Implement the actual processing workflow
    // For now, just return a placeholder
    Ok("Process started successfully".to_string())
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // Initialize logging
    env_logger::init();

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_process::init())
        .plugin(tauri_plugin_shell::init())
        .manage(AppState::default())
        .invoke_handler(tauri::generate_handler![
            select_iso_file,
            get_processing_status,
            update_config,
            start_microwin_process
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
