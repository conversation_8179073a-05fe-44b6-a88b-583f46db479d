{"$schema": "https://schema.tauri.app/config/2", "productName": "Microwin Linux", "version": "0.1.0", "identifier": "com.microwin-linux.app", "build": {"beforeDevCommand": "pnpm dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "pnpm build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "Microwin Linux - Windows ISO Customizer", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "resizable": true}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}