import { useState, useEffect } from "react";
import { invoke } from "@tauri-apps/api/core";
import { open } from "@tauri-apps/plugin-dialog";
import "./App.css";
import {
  WindowsImageInfo,
  MicrowinConfig,
  ProcessingStatus,
  defaultMicrowinConfig
} from "./types/microwin";

function App() {
  const [config, setConfig] = useState<MicrowinConfig>(defaultMicrowinConfig);
  const [windowsImages, setWindowsImages] = useState<WindowsImageInfo[]>([]);
  const [selectedImage, setSelectedImage] = useState<number | null>(null);
  const [status, setStatus] = useState<ProcessingStatus>({
    current_step: "Ready",
    progress: 0,
    completed: false,
    logs: []
  });
  const [isProcessing, setIsProcessing] = useState(false);

  // Poll for status updates
  useEffect(() => {
    const interval = setInterval(async () => {
      try {
        const currentStatus = await invoke<ProcessingStatus>("get_processing_status");
        setStatus(currentStatus);
        setIsProcessing(!currentStatus.completed && currentStatus.progress > 0);
      } catch (error) {
        console.error("Failed to get status:", error);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const selectIsoFile = async () => {
    try {
      const selected = await open({
        multiple: false,
        filters: [{
          name: 'ISO Files',
          extensions: ['iso']
        }]
      });

      if (selected && typeof selected === 'string') {
        setConfig(prev => ({ ...prev, iso_path: selected }));

        // Load Windows images from the ISO
        const images = await invoke<WindowsImageInfo[]>("select_iso_file", {
          isoPath: selected
        });
        setWindowsImages(images);

        // Auto-select the first image
        if (images.length > 0) {
          setSelectedImage(images[0].index);
        }
      }
    } catch (error) {
      console.error("Failed to select ISO:", error);
      alert(`Failed to select ISO: ${error}`);
    }
  };

  const selectOutputPath = async () => {
    try {
      const selected = await open({
        multiple: false,
        directory: false,
        filters: [{
          name: 'ISO Files',
          extensions: ['iso']
        }]
      });

      if (selected && typeof selected === 'string') {
        setConfig(prev => ({ ...prev, output_path: selected }));
      }
    } catch (error) {
      console.error("Failed to select output path:", error);
    }
  };

  const startProcessing = async () => {
    try {
      // Update configuration
      await invoke("update_config", { config });

      // Start processing
      setIsProcessing(true);
      const result = await invoke<string>("start_microwin_process");
      console.log("Processing started:", result);
    } catch (error) {
      console.error("Failed to start processing:", error);
      alert(`Failed to start processing: ${error}`);
      setIsProcessing(false);
    }
  };

  return (
    <main className="container">
      <header className="header">
        <h1>🪟 Microwin Linux</h1>
        <p>Create custom Windows ISOs on Linux</p>
      </header>

      <div className="content">
        {/* Step 1: ISO Selection */}
        <section className="step">
          <h2>Step 1: Select Windows ISO</h2>
          <div className="iso-selection">
            <button onClick={selectIsoFile} className="btn btn-primary">
              📁 Select Windows ISO
            </button>
            {config.iso_path && (
              <div className="selected-file">
                <p>Selected: {config.iso_path}</p>
              </div>
            )}
          </div>

          {windowsImages.length > 0 && (
            <div className="windows-images">
              <h3>Available Windows Editions:</h3>
              <div className="image-list">
                {windowsImages.map((image) => (
                  <div
                    key={image.index}
                    className={`image-item ${selectedImage === image.index ? 'selected' : ''}`}
                    onClick={() => setSelectedImage(image.index)}
                  >
                    <h4>{image.name}</h4>
                    <p>{image.description}</p>
                    <small>Version: {image.version} | Architecture: {image.architecture}</small>
                  </div>
                ))}
              </div>
            </div>
          )}
        </section>

        {/* Step 2: Configuration */}
        {windowsImages.length > 0 && (
          <section className="step">
            <h2>Step 2: Configuration</h2>

            {/* User Settings */}
            <div className="config-group">
              <h3>User Account</h3>
              <div className="form-row">
                <label>
                  Username:
                  <input
                    type="text"
                    value={config.user_settings.username}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      user_settings: { ...prev.user_settings, username: e.target.value }
                    }))}
                    placeholder="User"
                  />
                </label>
                <label>
                  Password (optional):
                  <input
                    type="password"
                    value={config.user_settings.password || ''}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      user_settings: { ...prev.user_settings, password: e.target.value || undefined }
                    }))}
                    placeholder="Leave empty for no password"
                  />
                </label>
              </div>
            </div>

            {/* Registry Settings */}
            <div className="config-group">
              <h3>System Modifications</h3>
              <div className="checkbox-group">
                <label className="checkbox">
                  <input
                    type="checkbox"
                    checked={config.registry_settings.bypass_requirements}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      registry_settings: { ...prev.registry_settings, bypass_requirements: e.target.checked }
                    }))}
                  />
                  Bypass Windows 11 hardware requirements
                </label>
                <label className="checkbox">
                  <input
                    type="checkbox"
                    checked={config.registry_settings.disable_telemetry}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      registry_settings: { ...prev.registry_settings, disable_telemetry: e.target.checked }
                    }))}
                  />
                  Disable telemetry and tracking
                </label>
                <label className="checkbox">
                  <input
                    type="checkbox"
                    checked={config.registry_settings.set_dark_theme}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      registry_settings: { ...prev.registry_settings, set_dark_theme: e.target.checked }
                    }))}
                  />
                  Set dark theme by default
                </label>
              </div>
            </div>

            {/* Debloating Settings */}
            <div className="config-group">
              <h3>Debloating Options</h3>
              <div className="checkbox-group">
                <label className="checkbox">
                  <input
                    type="checkbox"
                    checked={config.debloat_settings.remove_store_apps}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      debloat_settings: { ...prev.debloat_settings, remove_store_apps: e.target.checked }
                    }))}
                  />
                  Remove Microsoft Store apps
                </label>
                <label className="checkbox">
                  <input
                    type="checkbox"
                    checked={config.debloat_settings.remove_capabilities}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      debloat_settings: { ...prev.debloat_settings, remove_capabilities: e.target.checked }
                    }))}
                  />
                  Remove Windows capabilities (IE, WordPad, etc.)
                </label>
              </div>
            </div>

            {/* Output Path */}
            <div className="config-group">
              <h3>Output</h3>
              <div className="form-row">
                <button onClick={selectOutputPath} className="btn btn-secondary">
                  📁 Select Output Location
                </button>
                {config.output_path && (
                  <div className="selected-file">
                    <p>Output: {config.output_path}</p>
                  </div>
                )}
              </div>
            </div>
          </section>
        )}

        {/* Step 3: Processing */}
        {config.iso_path && config.output_path && (
          <section className="step">
            <h2>Step 3: Create Custom ISO</h2>

            <div className="processing-section">
              <button
                onClick={startProcessing}
                disabled={isProcessing}
                className="btn btn-success btn-large"
              >
                {isProcessing ? '⏳ Processing...' : '🚀 Start Processing'}
              </button>

              {/* Status Display */}
              <div className="status-display">
                <div className="status-text">
                  <strong>Status:</strong> {status.current_step}
                </div>
                {status.progress > 0 && (
                  <div className="progress-bar">
                    <div
                      className="progress-fill"
                      style={{ width: `${status.progress}%` }}
                    ></div>
                    <span className="progress-text">{status.progress}%</span>
                  </div>
                )}
                {status.error && (
                  <div className="error-message">
                    <strong>Error:</strong> {status.error}
                  </div>
                )}
              </div>

              {/* Logs */}
              {status.logs.length > 0 && (
                <div className="logs">
                  <h4>Processing Logs:</h4>
                  <div className="log-container">
                    {status.logs.map((log, index) => (
                      <div key={index} className="log-entry">{log}</div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </section>
        )}
      </div>
    </main>
  );
}

export default App;
