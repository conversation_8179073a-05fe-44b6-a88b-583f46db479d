/* Modern Microwin Linux Styling */
:root {
  --primary-color: #0078d4;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --border-radius: 8px;
  --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40px;
  color: white;
}

.header h1 {
  font-size: 3rem;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.header p {
  font-size: 1.2rem;
  margin: 10px 0 0 0;
  opacity: 0.9;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.step {
  background: white;
  border-radius: var(--border-radius);
  padding: 30px;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}

.step:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.step h2 {
  color: var(--dark-color);
  margin: 0 0 20px 0;
  font-size: 1.5rem;
  border-bottom: 2px solid var(--primary-color);
  padding-bottom: 10px;
}

/* Buttons */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: var(--border-radius);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #106ebe;
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #5a6268;
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-success:hover:not(:disabled) {
  background-color: #218838;
}

.btn-large {
  padding: 16px 32px;
  font-size: 1.2rem;
}

/* Form Elements */
.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.form-row label {
  flex: 1;
  min-width: 250px;
}

.form-row label input {
  width: 100%;
  margin-top: 5px;
}

input[type="text"],
input[type="password"] {
  padding: 10px;
  border: 2px solid #e0e0e0;
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: var(--transition);
}

input[type="text"]:focus,
input[type="password"]:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 120, 212, 0.1);
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.checkbox {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
}

.checkbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.config-group {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: var(--border-radius);
  border-left: 4px solid var(--primary-color);
}

.config-group h3 {
  margin: 0 0 15px 0;
  color: var(--dark-color);
  font-size: 1.2rem;
}

/* ISO Selection */
.iso-selection {
  margin-bottom: 20px;
}

.selected-file {
  margin-top: 15px;
  padding: 10px;
  background: #e8f5e8;
  border-radius: var(--border-radius);
  border-left: 4px solid var(--success-color);
}

.selected-file p {
  margin: 0;
  font-family: monospace;
  font-size: 0.9rem;
  color: var(--dark-color);
}

/* Windows Images */
.windows-images {
  margin-top: 20px;
}

.image-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.image-item {
  padding: 15px;
  border: 2px solid #e0e0e0;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  background: white;
}

.image-item:hover {
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.image-item.selected {
  border-color: var(--primary-color);
  background: #f0f8ff;
}

.image-item h4 {
  margin: 0 0 5px 0;
  color: var(--dark-color);
}

.image-item p {
  margin: 0 0 10px 0;
  color: var(--secondary-color);
  font-size: 0.9rem;
}

.image-item small {
  color: var(--secondary-color);
  font-size: 0.8rem;
}

/* Processing Section */
.processing-section {
  text-align: center;
}

.status-display {
  margin: 20px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: var(--border-radius);
  text-align: left;
}

.status-text {
  margin-bottom: 15px;
  font-size: 1.1rem;
}

.progress-bar {
  position: relative;
  width: 100%;
  height: 30px;
  background: #e0e0e0;
  border-radius: 15px;
  overflow: hidden;
  margin-bottom: 15px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--success-color), #20c997);
  transition: width 0.3s ease;
  border-radius: 15px;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-weight: bold;
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.error-message {
  padding: 15px;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: var(--border-radius);
  color: #721c24;
  margin-top: 15px;
}

/* Logs */
.logs {
  margin-top: 20px;
  text-align: left;
}

.logs h4 {
  margin: 0 0 10px 0;
  color: var(--dark-color);
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  background: #2d3748;
  border-radius: var(--border-radius);
  padding: 15px;
}

.log-entry {
  color: #e2e8f0;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  margin-bottom: 5px;
  line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 10px;
  }

  .header h1 {
    font-size: 2rem;
  }

  .step {
    padding: 20px;
  }

  .form-row {
    flex-direction: column;
  }

  .form-row label {
    min-width: auto;
  }

  .image-list {
    grid-template-columns: 1fr;
  }
}
