// TypeScript types for Microwin functionality

export interface WindowsImageInfo {
  index: number;
  name: string;
  description: string;
  version: string;
  architecture: string;
  size: number;
}

export interface UserSettings {
  username: string;
  password?: string;
  auto_login: boolean;
  language: string;
  timezone?: string;
}

export interface DriverSettings {
  inject_drivers: boolean;
  driver_path?: string;
  include_virtio: boolean;
  export_current_drivers: boolean;
}

export interface DebloatSettings {
  remove_store_apps: boolean;
  remove_capabilities: boolean;
  remove_features: boolean;
  custom_packages: string[];
  profile: DebloatProfile;
}

export interface RegistrySettings {
  bypass_requirements: boolean;
  disable_telemetry: boolean;
  configure_oobe: boolean;
  set_dark_theme: boolean;
  disable_windows_update: boolean;
}

export interface TempSettings {
  temp_dir?: string;
  mount_dir?: string;
  scratch_dir?: string;
  cleanup_on_completion: boolean;
}

export interface MicrowinConfig {
  iso_path: string;
  output_path: string;
  user_settings: UserSettings;
  driver_settings: DriverSettings;
  debloat_settings: DebloatSettings;
  registry_settings: RegistrySettings;
  temp_settings: TempSettings;
}

export interface ProcessingStatus {
  current_step: string;
  progress: number;
  completed: boolean;
  error?: string;
  logs: string[];
}

export type DebloatProfile = "Minimal" | "Standard" | "Aggressive" | "Custom";

// Default configurations
export const defaultUserSettings: UserSettings = {
  username: "User",
  password: undefined,
  auto_login: true,
  language: "en-US",
  timezone: undefined,
};

export const defaultDriverSettings: DriverSettings = {
  inject_drivers: false,
  driver_path: undefined,
  include_virtio: false,
  export_current_drivers: false,
};

export const defaultDebloatSettings: DebloatSettings = {
  remove_store_apps: true,
  remove_capabilities: true,
  remove_features: true,
  custom_packages: [],
  profile: "Standard",
};

export const defaultRegistrySettings: RegistrySettings = {
  bypass_requirements: true,
  disable_telemetry: true,
  configure_oobe: true,
  set_dark_theme: true,
  disable_windows_update: false,
};

export const defaultTempSettings: TempSettings = {
  temp_dir: undefined,
  mount_dir: undefined,
  scratch_dir: undefined,
  cleanup_on_completion: true,
};

export const defaultMicrowinConfig: MicrowinConfig = {
  iso_path: "",
  output_path: "",
  user_settings: defaultUserSettings,
  driver_settings: defaultDriverSettings,
  debloat_settings: defaultDebloatSettings,
  registry_settings: defaultRegistrySettings,
  temp_settings: defaultTempSettings,
};
