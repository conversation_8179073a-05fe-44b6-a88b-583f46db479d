use crate::config::{DebloatSettings, DebloatProfile};
use crate::errors::{MicrowinError, MicrowinResult, command_error};
use crate::microwin_error;
use std::path::{Path, PathBuf};
use std::process::Command;
use log::{info, warn, error};

/// Handles Windows package and feature removal using wimlib
pub struct Debloater {
    /// Path to the WIM file
    wim_path: PathBuf,
    /// Image index to modify
    image_index: u32,
}

impl Debloater {
    /// Create a new debloater for the given WIM file
    pub fn new(wim_path: PathBuf, image_index: u32) -> Self {
        Self {
            wim_path,
            image_index,
        }
    }

    /// Apply debloating based on settings
    pub fn apply_debloating(&self, settings: &DebloatSettings) -> MicrowinResult<()> {
        info!("Starting debloating process");

        if settings.remove_store_apps {
            self.remove_microsoft_store_apps(&settings.profile)?;
        }

        if settings.remove_capabilities {
            self.remove_windows_capabilities()?;
        }

        if settings.remove_features {
            self.remove_optional_features()?;
        }

        if !settings.custom_packages.is_empty() {
            self.remove_custom_packages(&settings.custom_packages)?;
        }

        info!("Debloating completed successfully");
        Ok(())
    }

    /// Remove Microsoft Store apps based on profile
    fn remove_microsoft_store_apps(&self, profile: &DebloatProfile) -> MicrowinResult<()> {
        info!("Removing Microsoft Store apps (profile: {:?})", profile);

        let packages_to_remove = match profile {
            DebloatProfile::Minimal => self.get_minimal_bloatware_list(),
            DebloatProfile::Standard => self.get_standard_bloatware_list(),
            DebloatProfile::Aggressive => self.get_aggressive_bloatware_list(),
            DebloatProfile::Custom => return Ok(()), // Custom packages handled separately
        };

        for package in packages_to_remove {
            self.remove_provisioned_package(&package)?;
        }

        Ok(())
    }

    /// Get minimal bloatware removal list
    fn get_minimal_bloatware_list(&self) -> Vec<&str> {
        vec![
            "Microsoft.BingNews",
            "Microsoft.BingWeather",
            "Microsoft.GetHelp",
            "Microsoft.Getstarted",
            "Microsoft.Microsoft3DViewer",
            "Microsoft.MicrosoftOfficeHub",
            "Microsoft.MicrosoftSolitaireCollection",
            "Microsoft.MixedReality.Portal",
            "Microsoft.Office.OneNote",
            "Microsoft.People",
            "Microsoft.Print3D",
            "Microsoft.SkypeApp",
            "Microsoft.Wallet",
            "Microsoft.WindowsAlarms",
            "Microsoft.WindowsCamera",
            "Microsoft.windowscommunicationsapps",
            "Microsoft.WindowsFeedbackHub",
            "Microsoft.WindowsMaps",
            "Microsoft.WindowsSoundRecorder",
            "Microsoft.Xbox.TCUI",
            "Microsoft.XboxApp",
            "Microsoft.XboxGameOverlay",
            "Microsoft.XboxGamingOverlay",
            "Microsoft.XboxIdentityProvider",
            "Microsoft.XboxSpeechToTextOverlay",
            "Microsoft.YourPhone",
            "Microsoft.ZuneMusic",
            "Microsoft.ZuneVideo",
        ]
    }

    /// Get standard bloatware removal list
    fn get_standard_bloatware_list(&self) -> Vec<&str> {
        let mut packages = self.get_minimal_bloatware_list();
        packages.extend_from_slice(&[
            "Microsoft.549981C3F5F10", // Cortana
            "Microsoft.BingSearch",
            "Microsoft.MicrosoftStickyNotes",
            "Microsoft.MSPaint",
            "Microsoft.PowerAutomateDesktop",
            "Microsoft.ScreenSketch",
            "Microsoft.Todos",
            "Microsoft.Windows.Photos",
            "Microsoft.WindowsCalculator",
            "Microsoft.WindowsStore",
            "MicrosoftTeams",
            "Microsoft.OneDriveSync",
        ]);
        packages
    }

    /// Get aggressive bloatware removal list
    fn get_aggressive_bloatware_list(&self) -> Vec<&str> {
        let mut packages = self.get_standard_bloatware_list();
        packages.extend_from_slice(&[
            "Microsoft.Windows.SecHealthUI",
            "Microsoft.Windows.ParentalControls",
            "Microsoft.BioEnrollment",
            "Windows.ContactSupport",
            "Windows.PrintDialog",
        ]);
        packages
    }

    /// Remove a provisioned package using wimlib
    fn remove_provisioned_package(&self, package_name: &str) -> MicrowinResult<()> {
        info!("Removing provisioned package: {}", package_name);

        let output = Command::new("wimlib-imagex")
            .args(&[
                "update",
                self.wim_path.to_str().unwrap(),
                &self.image_index.to_string(),
                "--command",
                &format!("delete /Windows/System32/config/systemprofile/AppData/Local/Packages/{}_*", package_name),
            ])
            .output()
            .map_err(|e| microwin_error!(WimError, "Failed to execute wimlib-imagex: {}", e))?;

        if !output.status.success() {
            warn!("Failed to remove package {}: {}", package_name, String::from_utf8_lossy(&output.stderr));
        }

        // Also try to remove from provisioned packages
        let output2 = Command::new("wimlib-imagex")
            .args(&[
                "update",
                self.wim_path.to_str().unwrap(),
                &self.image_index.to_string(),
                "--command",
                &format!("delete /ProgramData/Microsoft/Windows/AppRepository/Packages/{}_*", package_name),
            ])
            .output()
            .map_err(|e| microwin_error!(WimError, "Failed to execute wimlib-imagex: {}", e))?;

        if !output2.status.success() {
            warn!("Failed to remove provisioned package {}: {}", package_name, String::from_utf8_lossy(&output2.stderr));
        }

        Ok(())
    }

    /// Remove Windows capabilities
    fn remove_windows_capabilities(&self) -> MicrowinResult<()> {
        info!("Removing Windows capabilities");

        let capabilities_to_remove = vec![
            "Browser.InternetExplorer",
            "MathRecognizer",
            "OpenSSH.Client",
            "Microsoft.Windows.PowerShell.ISE",
            "App.Support.QuickAssist",
            "Media.WindowsMediaPlayer",
            "Microsoft.Windows.WordPad",
            "Print.Fax.Scan",
            "Microsoft.Windows.MSPaint",
        ];

        for capability in capabilities_to_remove {
            self.remove_capability(&capability)?;
        }

        Ok(())
    }

    /// Remove a specific Windows capability
    fn remove_capability(&self, capability_name: &str) -> MicrowinResult<()> {
        info!("Removing capability: {}", capability_name);

        // Create a temporary script to remove the capability
        let script_content = format!(
            r#"
try {{
    Get-WindowsCapability -Online -Name "*{}*" | Remove-WindowsCapability -Online -NoRestart
}} catch {{
    Write-Host "Failed to remove capability: {}"
}}
"#,
            capability_name, capability_name
        );

        let temp_dir = std::env::temp_dir();
        let script_path = temp_dir.join(format!("remove_{}.ps1", capability_name.replace(".", "_")));
        
        std::fs::write(&script_path, script_content)
            .map_err(|e| microwin_error!(WimError, "Failed to write PowerShell script: {}", e))?;

        // Note: This would need to be executed in the Windows environment
        // For now, we'll use wimlib to remove the capability files directly
        self.remove_capability_files(capability_name)?;

        let _ = std::fs::remove_file(&script_path);
        Ok(())
    }

    /// Remove capability files directly using wimlib
    fn remove_capability_files(&self, capability_name: &str) -> MicrowinResult<()> {
        let capability_paths = match capability_name {
            "Browser.InternetExplorer" => vec![
                "/Windows/System32/ieframe.dll",
                "/Windows/System32/iexplore.exe",
                "/Program Files/Internet Explorer",
            ],
            "Microsoft.Windows.WordPad" => vec![
                "/Program Files/Windows NT/Accessories/wordpad.exe",
            ],
            "Microsoft.Windows.MSPaint" => vec![
                "/Windows/System32/mspaint.exe",
            ],
            "Media.WindowsMediaPlayer" => vec![
                "/Program Files/Windows Media Player",
            ],
            _ => vec![],
        };

        for path in capability_paths {
            let output = Command::new("wimlib-imagex")
                .args(&[
                    "update",
                    self.wim_path.to_str().unwrap(),
                    &self.image_index.to_string(),
                    "--command",
                    &format!("delete {}", path),
                ])
                .output()
                .map_err(|e| microwin_error!(WimError, "Failed to execute wimlib-imagex: {}", e))?;

            if !output.status.success() {
                warn!("Failed to remove capability file {}: {}", path, String::from_utf8_lossy(&output.stderr));
            }
        }

        Ok(())
    }

    /// Remove optional Windows features
    fn remove_optional_features(&self) -> MicrowinResult<()> {
        info!("Removing optional Windows features");

        let features_to_remove = vec![
            "WindowsMediaPlayer",
            "Internet-Explorer-Optional-amd64",
            "WorkFolders-Client",
            "Printing-XPSServices-Features",
            "TelnetClient",
        ];

        for feature in features_to_remove {
            self.remove_optional_feature(&feature)?;
        }

        Ok(())
    }

    /// Remove a specific optional feature
    fn remove_optional_feature(&self, feature_name: &str) -> MicrowinResult<()> {
        info!("Removing optional feature: {}", feature_name);

        // Use DISM equivalent command with wimlib
        let output = Command::new("wimlib-imagex")
            .args(&[
                "update",
                self.wim_path.to_str().unwrap(),
                &self.image_index.to_string(),
                "--command",
                &format!("delete /Windows/WinSxS/*{}*", feature_name),
            ])
            .output()
            .map_err(|e| microwin_error!(WimError, "Failed to execute wimlib-imagex: {}", e))?;

        if !output.status.success() {
            warn!("Failed to remove feature {}: {}", feature_name, String::from_utf8_lossy(&output.stderr));
        }

        Ok(())
    }

    /// Remove custom packages specified by user
    fn remove_custom_packages(&self, packages: &[String]) -> MicrowinResult<()> {
        info!("Removing custom packages");

        for package in packages {
            self.remove_provisioned_package(package)?;
        }

        Ok(())
    }

    /// Get list of installed packages for inspection
    pub fn list_installed_packages(&self) -> MicrowinResult<Vec<String>> {
        info!("Listing installed packages");

        let output = Command::new("wimlib-imagex")
            .args(&[
                "dir",
                self.wim_path.to_str().unwrap(),
                &self.image_index.to_string(),
                "/Windows/System32/config/systemprofile/AppData/Local/Packages",
            ])
            .output()
            .map_err(|e| microwin_error!(WimError, "Failed to execute wimlib-imagex: {}", e))?;

        if !output.status.success() {
            return Err(command_error("wimlib-imagex dir", &output));
        }

        let output_str = String::from_utf8_lossy(&output.stdout);
        let packages: Vec<String> = output_str
            .lines()
            .filter_map(|line| {
                if line.contains("Microsoft.") || line.contains("Windows.") {
                    Some(line.trim().to_string())
                } else {
                    None
                }
            })
            .collect();

        Ok(packages)
    }
}
