use crate::config::{UserSettings, RegistrySettings};
use crate::errors::{MicrowinError, MicrowinResult};
use crate::microwin_error;
use std::path::Path;
use std::fs;
use log::info;

/// Generates unattend.xml files for automated Windows installation
pub struct UnattendGenerator {
    /// User settings for the installation
    user_settings: UserSettings,
    /// Registry settings to apply
    registry_settings: RegistrySettings,
}

impl UnattendGenerator {
    /// Create a new unattend generator
    pub fn new(user_settings: UserSettings, registry_settings: RegistrySettings) -> Self {
        Self {
            user_settings,
            registry_settings,
        }
    }

    /// Generate the complete unattend.xml file
    pub fn generate_unattend_xml(&self) -> MicrowinResult<String> {
        info!("Generating unattend.xml file");

        let xml_content = format!(
            r#"<?xml version="1.0" encoding="utf-8"?>
<unattend xmlns="urn:schemas-microsoft-com:unattend">
    <settings pass="windowsPE">
        <component name="Microsoft-Windows-International-Core-WinPE" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" language="neutral" versionScope="nonSxS" xmlns:wcm="http://schemas.microsoft.com/WMIConfig/2002/State" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <SetupUILanguage>
                <UILanguage>{}</UILanguage>
            </SetupUILanguage>
            <InputLocale>{}</InputLocale>
            <SystemLocale>{}</SystemLocale>
            <UILanguage>{}</UILanguage>
            <UserLocale>{}</UserLocale>
        </component>
        <component name="Microsoft-Windows-Setup" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" language="neutral" versionScope="nonSxS" xmlns:wcm="http://schemas.microsoft.com/WMIConfig/2002/State" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <DiskConfiguration>
                <Disk wcm:action="add">
                    <CreatePartitions>
                        <CreatePartition wcm:action="add">
                            <Order>1</Order>
                            <Type>Primary</Type>
                            <Extend>true</Extend>
                        </CreatePartition>
                    </CreatePartitions>
                    <ModifyPartitions>
                        <ModifyPartition wcm:action="add">
                            <Active>true</Active>
                            <Format>NTFS</Format>
                            <Label>Windows</Label>
                            <Order>1</Order>
                            <PartitionID>1</PartitionID>
                        </ModifyPartition>
                    </ModifyPartitions>
                    <DiskID>0</DiskID>
                    <WillWipeDisk>true</WillWipeDisk>
                </Disk>
            </DiskConfiguration>
            <ImageInstall>
                <OSImage>
                    <InstallTo>
                        <DiskID>0</DiskID>
                        <PartitionID>1</PartitionID>
                    </InstallTo>
                </OSImage>
            </ImageInstall>
            <UserData>
                <ProductKey>
                    <WillShowUI>OnError</WillShowUI>
                </ProductKey>
                <AcceptEula>true</AcceptEula>
                <FullName>{}</FullName>
                <Organization></Organization>
            </UserData>
            {}
        </component>
    </settings>
    <settings pass="offlineServicing">
        <component name="Microsoft-Windows-LUA-Settings" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" language="neutral" versionScope="nonSxS" xmlns:wcm="http://schemas.microsoft.com/WMIConfig/2002/State" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <EnableLUA>false</EnableLUA>
        </component>
    </settings>
    <settings pass="generalize">
        <component name="Microsoft-Windows-Security-SPP" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" language="neutral" versionScope="nonSxS" xmlns:wcm="http://schemas.microsoft.com/WMIConfig/2002/State" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <SkipRearm>1</SkipRearm>
        </component>
    </settings>
    <settings pass="specialize">
        <component name="Microsoft-Windows-International-Core" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" language="neutral" versionScope="nonSxS" xmlns:wcm="http://schemas.microsoft.com/WMIConfig/2002/State" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <InputLocale>{}</InputLocale>
            <SystemLocale>{}</SystemLocale>
            <UILanguage>{}</UILanguage>
            <UserLocale>{}</UserLocale>
        </component>
        <component name="Microsoft-Windows-Security-SPP-UX" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" language="neutral" versionScope="nonSxS" xmlns:wcm="http://schemas.microsoft.com/WMIConfig/2002/State" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <SkipAutoActivation>true</SkipAutoActivation>
        </component>
        <component name="Microsoft-Windows-SQMApi" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" language="neutral" versionScope="nonSxS" xmlns:wcm="http://schemas.microsoft.com/WMIConfig/2002/State" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <CEIPEnabled>0</CEIPEnabled>
        </component>
        <component name="Microsoft-Windows-Shell-Setup" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" language="neutral" versionScope="nonSxS" xmlns:wcm="http://schemas.microsoft.com/WMIConfig/2002/State" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <ComputerName>*</ComputerName>
            {}
        </component>
    </settings>
    <settings pass="oobeSystem">
        <component name="Microsoft-Windows-Shell-Setup" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" language="neutral" versionScope="nonSxS" xmlns:wcm="http://schemas.microsoft-com:unattend">
            <AutoLogon>
                <Password>
                    <Value>{}</Value>
                    <PlainText>true</PlainText>
                </Password>
                <Enabled>{}</Enabled>
                <Username>{}</Username>
            </AutoLogon>
            <OOBE>
                <HideEULAPage>true</HideEULAPage>
                <HideOEMRegistrationScreen>true</HideOEMRegistrationScreen>
                <HideOnlineAccountScreens>true</HideOnlineAccountScreens>
                <HideWirelessSetupInOOBE>true</HideWirelessSetupInOOBE>
                <NetworkLocation>Home</NetworkLocation>
                <SkipUserOOBE>true</SkipUserOOBE>
                <SkipMachineOOBE>true</SkipMachineOOBE>
            </OOBE>
            <UserAccounts>
                <LocalAccounts>
                    <LocalAccount wcm:action="add">
                        <Password>
                            <Value>{}</Value>
                            <PlainText>true</PlainText>
                        </Password>
                        <Group>Administrators</Group>
                        <DisplayName>{}</DisplayName>
                        <Name>{}</Name>
                        <Description>Microwin User Account</Description>
                    </LocalAccount>
                </LocalAccounts>
            </UserAccounts>
            <RegisteredOrganization></RegisteredOrganization>
            <RegisteredOwner>{}</RegisteredOwner>
            <DisableAutoDaylightTimeSet>false</DisableAutoDaylightTimeSet>
            <FirstLogonCommands>
                <SynchronousCommand wcm:action="add">
                    <Description>Control Panel View</Description>
                    <Order>1</Order>
                    <CommandLine>reg add "HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\ControlPanel" /v StartupPage /t REG_DWORD /d 1 /f</CommandLine>
                    <RequiresUserInput>false</RequiresUserInput>
                </SynchronousCommand>
                <SynchronousCommand wcm:action="add">
                    <Description>Control Panel Icon Size</Description>
                    <Order>2</Order>
                    <CommandLine>reg add "HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\ControlPanel" /v AllItemsIconView /t REG_DWORD /d 0 /f</CommandLine>
                    <RequiresUserInput>false</RequiresUserInput>
                </SynchronousCommand>
                {}
            </FirstLogonCommands>
            {}
        </component>
        <component name="Microsoft-Windows-International-Core" processorArchitecture="amd64" publicKeyToken="31bf3856ad364e35" language="neutral" versionScope="nonSxS" xmlns:wcm="http://schemas.microsoft.com/WMIConfig/2002/State" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
            <InputLocale>{}</InputLocale>
            <SystemLocale>{}</SystemLocale>
            <UILanguage>{}</UILanguage>
            <UserLocale>{}</UserLocale>
        </component>
    </settings>
</unattend>"#,
            // windowsPE settings
            self.user_settings.language,
            self.user_settings.language,
            self.user_settings.language,
            self.user_settings.language,
            self.user_settings.language,
            self.user_settings.username,
            self.generate_bypass_checks(),
            // specialize settings
            self.user_settings.language,
            self.user_settings.language,
            self.user_settings.language,
            self.user_settings.language,
            self.generate_timezone_setting(),
            // oobeSystem settings
            self.user_settings.password.as_deref().unwrap_or(""),
            if self.user_settings.auto_login { "true" } else { "false" },
            self.user_settings.username,
            self.user_settings.password.as_deref().unwrap_or(""),
            self.user_settings.username,
            self.user_settings.username,
            self.user_settings.username,
            self.generate_first_logon_commands(),
            self.generate_timezone_setting(),
            self.user_settings.language,
            self.user_settings.language,
            self.user_settings.language,
            self.user_settings.language,
        );

        Ok(xml_content)
    }

    /// Generate Windows 11 bypass checks
    fn generate_bypass_checks(&self) -> String {
        if self.registry_settings.bypass_requirements {
            r#"<RunSynchronous>
                <RunSynchronousCommand wcm:action="add">
                    <Description>Bypass TPM Check</Description>
                    <Order>1</Order>
                    <Path>reg add "HKLM\SYSTEM\Setup\LabConfig" /v "BypassTPMCheck" /t REG_DWORD /d 1 /f</Path>
                </RunSynchronousCommand>
                <RunSynchronousCommand wcm:action="add">
                    <Description>Bypass Secure Boot Check</Description>
                    <Order>2</Order>
                    <Path>reg add "HKLM\SYSTEM\Setup\LabConfig" /v "BypassSecureBootCheck" /t REG_DWORD /d 1 /f</Path>
                </RunSynchronousCommand>
                <RunSynchronousCommand wcm:action="add">
                    <Description>Bypass RAM Check</Description>
                    <Order>3</Order>
                    <Path>reg add "HKLM\SYSTEM\Setup\LabConfig" /v "BypassRAMCheck" /t REG_DWORD /d 1 /f</Path>
                </RunSynchronousCommand>
                <RunSynchronousCommand wcm:action="add">
                    <Description>Bypass Storage Check</Description>
                    <Order>4</Order>
                    <Path>reg add "HKLM\SYSTEM\Setup\LabConfig" /v "BypassStorageCheck" /t REG_DWORD /d 1 /f</Path>
                </RunSynchronousCommand>
                <RunSynchronousCommand wcm:action="add">
                    <Description>Bypass CPU Check</Description>
                    <Order>5</Order>
                    <Path>reg add "HKLM\SYSTEM\Setup\LabConfig" /v "BypassCPUCheck" /t REG_DWORD /d 1 /f</Path>
                </RunSynchronousCommand>
            </RunSynchronous>"#.to_string()
        } else {
            String::new()
        }
    }

    /// Generate timezone setting
    fn generate_timezone_setting(&self) -> String {
        if let Some(timezone) = &self.user_settings.timezone {
            format!(
                r#"<TimeZone>{}</TimeZone>"#,
                timezone
            )
        } else {
            String::new()
        }
    }

    /// Generate first logon commands
    fn generate_first_logon_commands(&self) -> String {
        let mut commands = Vec::new();
        let mut order = 3; // Start after the base commands

        if self.registry_settings.disable_telemetry {
            commands.push(format!(
                r#"<SynchronousCommand wcm:action="add">
                    <Description>Disable Telemetry</Description>
                    <Order>{}</Order>
                    <CommandLine>reg add "HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows\DataCollection" /v AllowTelemetry /t REG_DWORD /d 0 /f</CommandLine>
                    <RequiresUserInput>false</RequiresUserInput>
                </SynchronousCommand>"#,
                order
            ));
            order += 1;
        }

        if self.registry_settings.set_dark_theme {
            commands.push(format!(
                r#"<SynchronousCommand wcm:action="add">
                    <Description>Set Dark Theme</Description>
                    <Order>{}</Order>
                    <CommandLine>reg add "HKEY_CURRENT_USER\SOFTWARE\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v AppsUseLightTheme /t REG_DWORD /d 0 /f</CommandLine>
                    <RequiresUserInput>false</RequiresUserInput>
                </SynchronousCommand>"#,
                order
            ));
            order += 1;

            commands.push(format!(
                r#"<SynchronousCommand wcm:action="add">
                    <Description>Set Dark System Theme</Description>
                    <Order>{}</Order>
                    <CommandLine>reg add "HKEY_CURRENT_USER\SOFTWARE\Microsoft\Windows\CurrentVersion\Themes\Personalize" /v SystemUsesLightTheme /t REG_DWORD /d 0 /f</CommandLine>
                    <RequiresUserInput>false</RequiresUserInput>
                </SynchronousCommand>"#,
                order
            ));
        }

        commands.join("\n                ")
    }

    /// Generate FirstStartup.ps1 script for post-installation tasks
    pub fn generate_first_startup_script(&self) -> MicrowinResult<String> {
        info!("Generating FirstStartup.ps1 script");

        let script_content = format!(
            r#"# Microwin FirstStartup Script
# This script runs on first boot to complete the setup

Write-Host "Starting Microwin post-installation setup..."

# Set execution policy
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Force

# Disable Windows Update if requested
{}

# Additional telemetry cleanup
{}

# Set up user preferences
{}

# Clean up temporary files
Remove-Item -Path "C:\Windows\Temp\*" -Recurse -Force -ErrorAction SilentlyContinue
Remove-Item -Path "C:\Users\<USER>\AppData\Local\Temp\*" -Recurse -Force -ErrorAction SilentlyContinue

Write-Host "Microwin setup completed successfully!"

# Self-delete this script
Remove-Item -Path $MyInvocation.MyCommand.Path -Force
"#,
            if self.registry_settings.disable_windows_update {
                r#"
# Disable Windows Update
try {
    Set-Service -Name wuauserv -StartupType Disabled -ErrorAction Stop
    Stop-Service -Name wuauserv -Force -ErrorAction Stop
    Write-Host "Windows Update service disabled"
} catch {
    Write-Host "Failed to disable Windows Update: $_"
}
"#
            } else {
                ""
            },
            if self.registry_settings.disable_telemetry {
                r#"
# Additional telemetry cleanup
try {
    # Disable scheduled tasks related to telemetry
    Get-ScheduledTask -TaskPath "\Microsoft\Windows\Application Experience\" | Disable-ScheduledTask -ErrorAction SilentlyContinue
    Get-ScheduledTask -TaskPath "\Microsoft\Windows\Customer Experience Improvement Program\" | Disable-ScheduledTask -ErrorAction SilentlyContinue
    Get-ScheduledTask -TaskPath "\Microsoft\Windows\DiskDiagnostic\" | Disable-ScheduledTask -ErrorAction SilentlyContinue
    Write-Host "Telemetry tasks disabled"
} catch {
    Write-Host "Failed to disable some telemetry tasks: $_"
}
"#
            } else {
                ""
            },
            r#"
# Set Windows Explorer preferences
try {
    # Show file extensions
    Set-ItemProperty -Path "HKCU:\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" -Name "HideFileExt" -Value 0
    # Show hidden files
    Set-ItemProperty -Path "HKCU:\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" -Name "Hidden" -Value 1
    Write-Host "Explorer preferences set"
} catch {
    Write-Host "Failed to set Explorer preferences: $_"
}
"#
        );

        Ok(script_content)
    }

    /// Save unattend.xml to the specified path
    pub fn save_unattend_xml(&self, output_path: &Path) -> MicrowinResult<()> {
        let xml_content = self.generate_unattend_xml()?;
        
        fs::write(output_path, xml_content)
            .map_err(|e| microwin_error!(FileSystemError, "Failed to write unattend.xml: {}", e))?;

        info!("Unattend.xml saved to: {}", output_path.display());
        Ok(())
    }

    /// Save FirstStartup.ps1 script to the specified path
    pub fn save_first_startup_script(&self, output_path: &Path) -> MicrowinResult<()> {
        let script_content = self.generate_first_startup_script()?;
        
        fs::write(output_path, script_content)
            .map_err(|e| microwin_error!(FileSystemError, "Failed to write FirstStartup.ps1: {}", e))?;

        info!("FirstStartup.ps1 saved to: {}", output_path.display());
        Ok(())
    }
}
