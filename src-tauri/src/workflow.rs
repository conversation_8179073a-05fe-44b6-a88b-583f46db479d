use crate::config::{MicrowinConfig, ProcessingStatus};
use crate::errors::{MicrowinError, MicrowinResult};
use crate::iso_handler::IsoHandler;
use crate::registry_editor::RegistryEditor;
use crate::debloater::Debloater;
use crate::unattend_generator::UnattendGenerator;
use crate::driver_injector::DriverInjector;
use crate::iso_builder::IsoBuilder;
use std::path::{Path, PathBuf};
use std::fs;
use std::sync::{Arc, Mutex};
use log::{info, error};
use uuid::Uuid;

/// Main workflow orchestrator for Microwin processing
pub struct MicrowinWorkflow {
    /// Configuration for the processing
    config: MicrowinConfig,
    /// Status tracking
    status: Arc<Mutex<ProcessingStatus>>,
    /// Working directory for temporary files
    work_dir: PathBuf,
    /// ISO handler instance
    iso_handler: Option<IsoHandler>,
}

impl MicrowinWorkflow {
    /// Create a new workflow instance
    pub fn new(config: MicrowinConfig, status: Arc<Mutex<ProcessingStatus>>) -> MicrowinResult<Self> {
        let work_dir = std::env::temp_dir().join(format!("microwin_work_{}", Uuid::new_v4()));
        fs::create_dir_all(&work_dir)?;

        Ok(Self {
            config,
            status,
            work_dir,
            iso_handler: None,
        })
    }

    /// Execute the complete Microwin workflow
    pub async fn execute(&mut self) -> MicrowinResult<String> {
        info!("Starting Microwin workflow execution");

        // Step 1: Mount and validate ISO
        self.update_status("Mounting Windows ISO...", 5).await;
        self.mount_and_validate_iso().await?;

        // Step 2: Copy ISO contents to working directory
        self.update_status("Copying ISO contents...", 15).await;
        let iso_work_dir = self.copy_iso_contents().await?;

        // Step 3: Extract and modify WIM file
        self.update_status("Extracting Windows image...", 25).await;
        let wim_path = self.extract_wim_file(&iso_work_dir).await?;

        // Step 4: Apply registry modifications
        self.update_status("Applying registry modifications...", 35).await;
        self.apply_registry_modifications(&wim_path).await?;

        // Step 5: Remove bloatware
        self.update_status("Removing bloatware...", 50).await;
        self.apply_debloating(&wim_path).await?;

        // Step 6: Inject drivers
        self.update_status("Injecting drivers...", 65).await;
        self.inject_drivers(&wim_path).await?;

        // Step 7: Generate unattend files
        self.update_status("Generating unattend files...", 75).await;
        self.generate_unattend_files(&iso_work_dir).await?;

        // Step 8: Rebuild WIM file
        self.update_status("Rebuilding Windows image...", 85).await;
        self.rebuild_wim_file(&wim_path, &iso_work_dir).await?;

        // Step 9: Create bootable ISO
        self.update_status("Creating bootable ISO...", 95).await;
        self.create_bootable_iso(&iso_work_dir).await?;

        // Step 10: Cleanup and finalize
        self.update_status("Finalizing...", 100).await;
        self.cleanup().await?;

        self.update_status("Microwin processing completed successfully!", 100).await;
        Ok(self.config.output_path.clone())
    }

    /// Mount and validate the input ISO
    async fn mount_and_validate_iso(&mut self) -> MicrowinResult<()> {
        let mut handler = IsoHandler::new();
        let mount_point = handler.mount_iso(Path::new(&self.config.iso_path))?;
        
        // Validate that it's a Windows ISO
        let images = handler.get_wim_info()?;
        if images.is_empty() {
            return Err(MicrowinError::ValidationError("No Windows images found in ISO".to_string()));
        }

        info!("Found {} Windows images in ISO", images.len());
        self.iso_handler = Some(handler);
        Ok(())
    }

    /// Copy ISO contents to working directory
    async fn copy_iso_contents(&self) -> MicrowinResult<PathBuf> {
        let iso_work_dir = self.work_dir.join("iso_contents");
        fs::create_dir_all(&iso_work_dir)?;

        if let Some(handler) = &self.iso_handler {
            handler.copy_iso_contents(&iso_work_dir)?;
        }

        Ok(iso_work_dir)
    }

    /// Extract WIM file for modification
    async fn extract_wim_file(&self, iso_dir: &Path) -> MicrowinResult<PathBuf> {
        let wim_path = iso_dir.join("sources").join("install.wim");
        if !wim_path.exists() {
            let esd_path = iso_dir.join("sources").join("install.esd");
            if esd_path.exists() {
                // Convert ESD to WIM
                self.convert_esd_to_wim(&esd_path, &wim_path).await?;
            } else {
                return Err(MicrowinError::IsoError("No install.wim or install.esd found".to_string()));
            }
        }

        Ok(wim_path)
    }

    /// Convert ESD to WIM format
    async fn convert_esd_to_wim(&self, esd_path: &Path, wim_path: &Path) -> MicrowinResult<()> {
        info!("Converting ESD to WIM format");
        
        use std::process::Command;
        let output = Command::new("wimlib-imagex")
            .args(&[
                "export",
                esd_path.to_str().unwrap(),
                "1",
                wim_path.to_str().unwrap(),
                "--compress=maximum",
            ])
            .output()?;

        if !output.status.success() {
            return Err(MicrowinError::WimError(format!(
                "Failed to convert ESD to WIM: {}",
                String::from_utf8_lossy(&output.stderr)
            )));
        }

        Ok(())
    }

    /// Apply registry modifications
    async fn apply_registry_modifications(&self, wim_path: &Path) -> MicrowinResult<()> {
        // Mount the WIM file to access registry hives
        let mount_dir = self.work_dir.join("wim_mount");
        fs::create_dir_all(&mount_dir)?;

        self.mount_wim(wim_path, &mount_dir).await?;

        // Apply registry modifications
        let mut registry_editor = RegistryEditor::new(mount_dir.clone());
        registry_editor.apply_microwin_tweaks(&self.config.registry_settings)?;

        // Unmount the WIM
        self.unmount_wim(&mount_dir).await?;

        Ok(())
    }

    /// Apply debloating
    async fn apply_debloating(&self, wim_path: &Path) -> MicrowinResult<()> {
        let debloater = Debloater::new(wim_path.to_path_buf(), 1);
        debloater.apply_debloating(&self.config.debloat_settings)?;
        Ok(())
    }

    /// Inject drivers
    async fn inject_drivers(&self, wim_path: &Path) -> MicrowinResult<()> {
        if self.config.driver_settings.inject_drivers || self.config.driver_settings.include_virtio {
            let driver_injector = DriverInjector::new(wim_path.to_path_buf(), 1)?;
            driver_injector.inject_drivers(&self.config.driver_settings)?;
        }
        Ok(())
    }

    /// Generate unattend files
    async fn generate_unattend_files(&self, iso_dir: &Path) -> MicrowinResult<()> {
        let generator = UnattendGenerator::new(
            self.config.user_settings.clone(),
            self.config.registry_settings.clone(),
        );

        // Generate and save unattend.xml
        let unattend_path = iso_dir.join("autounattend.xml");
        generator.save_unattend_xml(&unattend_path)?;

        // Generate and save FirstStartup.ps1
        let startup_script_path = iso_dir.join("sources").join("FirstStartup.ps1");
        generator.save_first_startup_script(&startup_script_path)?;

        Ok(())
    }

    /// Rebuild WIM file
    async fn rebuild_wim_file(&self, wim_path: &Path, iso_dir: &Path) -> MicrowinResult<()> {
        // The WIM file should already be modified in place
        // Just verify it's still valid
        use std::process::Command;
        let output = Command::new("wimlib-imagex")
            .args(&["info", wim_path.to_str().unwrap()])
            .output()?;

        if !output.status.success() {
            return Err(MicrowinError::WimError("WIM file validation failed".to_string()));
        }

        Ok(())
    }

    /// Create bootable ISO
    async fn create_bootable_iso(&self, iso_dir: &Path) -> MicrowinResult<()> {
        let output_path = PathBuf::from(&self.config.output_path);
        let builder = IsoBuilder::new(
            iso_dir.to_path_buf(),
            output_path,
            Some("MICROWIN".to_string()),
        );

        builder.build_bootable_iso()?;
        builder.optimize_iso()?;

        Ok(())
    }

    /// Mount WIM file
    async fn mount_wim(&self, wim_path: &Path, mount_dir: &Path) -> MicrowinResult<()> {
        use std::process::Command;
        let output = Command::new("wimlib-imagex")
            .args(&[
                "mountrw",
                wim_path.to_str().unwrap(),
                "1",
                mount_dir.to_str().unwrap(),
            ])
            .output()?;

        if !output.status.success() {
            return Err(MicrowinError::WimError(format!(
                "Failed to mount WIM: {}",
                String::from_utf8_lossy(&output.stderr)
            )));
        }

        Ok(())
    }

    /// Unmount WIM file
    async fn unmount_wim(&self, mount_dir: &Path) -> MicrowinResult<()> {
        use std::process::Command;
        let output = Command::new("wimlib-imagex")
            .args(&[
                "unmount",
                mount_dir.to_str().unwrap(),
                "--commit",
            ])
            .output()?;

        if !output.status.success() {
            return Err(MicrowinError::WimError(format!(
                "Failed to unmount WIM: {}",
                String::from_utf8_lossy(&output.stderr)
            )));
        }

        Ok(())
    }

    /// Update processing status
    async fn update_status(&self, message: &str, progress: u8) {
        if let Ok(mut status) = self.status.lock() {
            status.current_step = message.to_string();
            status.progress = progress;
            status.logs.push(format!("[{}] {}", chrono::Utc::now().format("%H:%M:%S"), message));

            // Keep only last 50 log entries - temporarily disabled to avoid borrowing issues
            // TODO: Fix the borrowing issue with log trimming
            // if status.logs.len() > 50 {
            //     status.logs = status.logs.split_off(status.logs.len() - 50);
            // }
        }

        info!("{} ({}%)", message, progress);
    }

    /// Cleanup temporary files
    async fn cleanup(&self) -> MicrowinResult<()> {
        info!("Cleaning up temporary files");

        // Unmount ISO if still mounted
        if let Some(handler) = &self.iso_handler {
            // The Drop implementation will handle cleanup
        }

        // Remove working directory
        if self.work_dir.exists() {
            fs::remove_dir_all(&self.work_dir)?;
        }

        Ok(())
    }
}

impl Drop for MicrowinWorkflow {
    fn drop(&mut self) {
        // Ensure cleanup happens even if the workflow fails
        if self.work_dir.exists() {
            if let Err(e) = fs::remove_dir_all(&self.work_dir) {
                error!("Failed to cleanup workflow directory: {}", e);
            }
        }
    }
}
