use crate::errors::{<PERSON><PERSON><PERSON><PERSON>r, MicrowinResult, command_error};
use crate::microwin_error;
use std::path::{Path, PathBuf};
use std::process::Command;
use std::fs;
use log::{info, warn, error};

/// Handles creation of bootable Windows ISOs
pub struct IsoBuilder {
    /// Source directory containing the modified Windows files
    source_dir: PathBuf,
    /// Output path for the new ISO
    output_path: PathBuf,
    /// ISO label
    label: String,
}

impl IsoBuilder {
    /// Create a new ISO builder
    pub fn new(source_dir: PathBuf, output_path: PathBuf, label: Option<String>) -> Self {
        Self {
            source_dir,
            output_path,
            label: label.unwrap_or_else(|| "MICROWIN".to_string()),
        }
    }

    /// Build a bootable Windows ISO
    pub fn build_bootable_iso(&self) -> MicrowinResult<()> {
        info!("Building bootable ISO: {}", self.output_path.display());

        // Validate source directory
        self.validate_source_directory()?;

        // Find boot files
        let boot_files = self.find_boot_files()?;

        // Create the ISO using genisoimage
        self.create_iso_with_genisoimage(&boot_files)?;

        // Validate the created ISO
        self.validate_created_iso()?;

        info!("Bootable ISO created successfully: {}", self.output_path.display());
        Ok(())
    }

    /// Validate that the source directory contains necessary Windows files
    fn validate_source_directory(&self) -> MicrowinResult<()> {
        info!("Validating source directory: {}", self.source_dir.display());

        if !self.source_dir.exists() {
            return Err(microwin_error!(IsoError, "Source directory does not exist: {}", self.source_dir.display()));
        }

        // Check for essential Windows files
        let required_files = vec![
            "sources/install.wim",
            "sources/boot.wim",
            "bootmgr",
            "setup.exe",
        ];

        for file in required_files {
            let file_path = self.source_dir.join(file);
            if !file_path.exists() {
                return Err(microwin_error!(IsoError, "Required file missing: {}", file));
            }
        }

        info!("Source directory validation passed");
        Ok(())
    }

    /// Find boot files for UEFI and BIOS
    fn find_boot_files(&self) -> MicrowinResult<BootFiles> {
        info!("Finding boot files");

        let mut boot_files = BootFiles::default();

        // BIOS boot file
        let etfsboot_path = self.source_dir.join("boot/etfsboot.com");
        if etfsboot_path.exists() {
            boot_files.bios_boot = Some(etfsboot_path);
        }

        // UEFI boot file
        let efisys_path = self.source_dir.join("efi/microsoft/boot/efisys.bin");
        if efisys_path.exists() {
            boot_files.uefi_boot = Some(efisys_path);
        } else {
            // Alternative UEFI boot file location
            let efisys_alt_path = self.source_dir.join("efi/boot/bootx64.efi");
            if efisys_alt_path.exists() {
                boot_files.uefi_boot = Some(efisys_alt_path);
            }
        }

        if boot_files.bios_boot.is_none() && boot_files.uefi_boot.is_none() {
            return Err(microwin_error!(IsoError, "No boot files found for BIOS or UEFI"));
        }

        info!("Boot files found - BIOS: {:?}, UEFI: {:?}", 
              boot_files.bios_boot.is_some(), boot_files.uefi_boot.is_some());
        Ok(boot_files)
    }

    /// Create ISO using genisoimage with proper boot configuration
    fn create_iso_with_genisoimage(&self, boot_files: &BootFiles) -> MicrowinResult<()> {
        info!("Creating ISO with genisoimage");

        let mut args = vec![
            "-iso-level", "4",
            "-l", "-R", "-J",
            "-v", "-d", "-N",
            "-joliet-long",
            "-relaxed-filenames",
            "-V", &self.label,
            "-o", self.output_path.to_str().unwrap(),
        ];

        // Add BIOS boot configuration
        if let Some(bios_boot) = &boot_files.bios_boot {
            args.extend_from_slice(&[
                "-b", "boot/etfsboot.com",
                "-no-emul-boot",
                "-boot-load-size", "8",
                "-hide", "boot/etfsboot.com",
            ]);
        }

        // Add UEFI boot configuration
        if let Some(uefi_boot) = &boot_files.uefi_boot {
            // For UEFI, we need to create an El Torito catalog
            args.extend_from_slice(&[
                "-eltorito-alt-boot",
                "-eltorito-platform", "efi",
                "-eltorito-boot", "efi/microsoft/boot/efisys.bin",
                "-no-emul-boot",
            ]);
        }

        // Add source directory
        args.push(self.source_dir.to_str().unwrap());

        // Execute genisoimage
        let output = Command::new("genisoimage")
            .args(&args)
            .output()
            .map_err(|e| microwin_error!(IsoError, "Failed to execute genisoimage: {}", e))?;

        if !output.status.success() {
            return Err(command_error("genisoimage", &output));
        }

        Ok(())
    }

    /// Validate the created ISO file
    fn validate_created_iso(&self) -> MicrowinResult<()> {
        info!("Validating created ISO");

        if !self.output_path.exists() {
            return Err(microwin_error!(IsoError, "ISO file was not created"));
        }

        // Check file size (should be reasonable for a Windows ISO)
        let metadata = fs::metadata(&self.output_path)
            .map_err(|e| microwin_error!(IsoError, "Failed to read ISO metadata: {}", e))?;

        let file_size = metadata.len();
        if file_size < 1_000_000_000 { // Less than 1GB is suspicious for Windows
            warn!("ISO file size seems small: {} bytes", file_size);
        }

        // Try to mount and verify the ISO structure
        self.verify_iso_structure()?;

        info!("ISO validation completed successfully");
        Ok(())
    }

    /// Verify ISO structure by mounting and checking contents
    fn verify_iso_structure(&self) -> MicrowinResult<()> {
        info!("Verifying ISO structure");

        let temp_mount = std::env::temp_dir().join(format!("microwin_verify_{}", uuid::Uuid::new_v4()));
        fs::create_dir_all(&temp_mount)?;

        // Mount the ISO
        let mount_output = Command::new("sudo")
            .args(&[
                "mount",
                "-o", "loop,ro",
                self.output_path.to_str().unwrap(),
                temp_mount.to_str().unwrap(),
            ])
            .output()
            .map_err(|e| microwin_error!(IsoError, "Failed to mount ISO for verification: {}", e))?;

        if !mount_output.status.success() {
            let _ = fs::remove_dir_all(&temp_mount);
            return Err(command_error("mount", &mount_output));
        }

        // Check for essential files
        let essential_files = vec![
            "bootmgr",
            "setup.exe",
            "sources/install.wim",
            "sources/boot.wim",
        ];

        let mut verification_passed = true;
        for file in essential_files {
            let file_path = temp_mount.join(file);
            if !file_path.exists() {
                error!("Essential file missing in ISO: {}", file);
                verification_passed = false;
            }
        }

        // Unmount the ISO
        let unmount_output = Command::new("sudo")
            .args(&["umount", temp_mount.to_str().unwrap()])
            .output()
            .map_err(|e| microwin_error!(IsoError, "Failed to unmount verification ISO: {}", e))?;

        if !unmount_output.status.success() {
            warn!("Failed to unmount verification ISO: {}", String::from_utf8_lossy(&unmount_output.stderr));
        }

        // Cleanup
        let _ = fs::remove_dir_all(&temp_mount);

        if !verification_passed {
            return Err(microwin_error!(IsoError, "ISO structure verification failed"));
        }

        Ok(())
    }

    /// Optimize ISO for size and performance
    pub fn optimize_iso(&self) -> MicrowinResult<()> {
        info!("Optimizing ISO");

        // Check if the ISO can be compressed further
        let original_size = fs::metadata(&self.output_path)
            .map_err(|e| microwin_error!(IsoError, "Failed to read ISO size: {}", e))?
            .len();

        // Create a compressed version using xz
        let compressed_path = self.output_path.with_extension("iso.xz");
        
        let output = Command::new("xz")
            .args(&[
                "-9",
                "-k", // Keep original file
                self.output_path.to_str().unwrap(),
            ])
            .output()
            .map_err(|e| microwin_error!(IsoError, "Failed to compress ISO: {}", e))?;

        if output.status.success() {
            let compressed_size = fs::metadata(&compressed_path)
                .map_err(|e| microwin_error!(IsoError, "Failed to read compressed ISO size: {}", e))?
                .len();

            let compression_ratio = (compressed_size as f64 / original_size as f64) * 100.0;
            info!("ISO compressed: {:.1}% of original size", compression_ratio);

            // If compression is significant, keep the compressed version
            if compression_ratio < 80.0 {
                info!("Keeping compressed version due to significant size reduction");
            } else {
                // Remove compressed version if not much smaller
                let _ = fs::remove_file(&compressed_path);
            }
        } else {
            warn!("Failed to compress ISO: {}", String::from_utf8_lossy(&output.stderr));
        }

        Ok(())
    }

    /// Get ISO file information
    pub fn get_iso_info(&self) -> MicrowinResult<IsoInfo> {
        if !self.output_path.exists() {
            return Err(microwin_error!(IsoError, "ISO file does not exist"));
        }

        let metadata = fs::metadata(&self.output_path)
            .map_err(|e| microwin_error!(IsoError, "Failed to read ISO metadata: {}", e))?;

        Ok(IsoInfo {
            path: self.output_path.clone(),
            size: metadata.len(),
            label: self.label.clone(),
            created: metadata.created().ok(),
        })
    }
}

/// Boot files structure
#[derive(Debug, Default)]
struct BootFiles {
    bios_boot: Option<PathBuf>,
    uefi_boot: Option<PathBuf>,
}

/// ISO information structure
#[derive(Debug)]
pub struct IsoInfo {
    pub path: PathBuf,
    pub size: u64,
    pub label: String,
    pub created: Option<std::time::SystemTime>,
}

/// Helper function to format file size
pub fn format_file_size(size: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    let mut size = size as f64;
    let mut unit_index = 0;

    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }

    format!("{:.1} {}", size, UNITS[unit_index])
}
